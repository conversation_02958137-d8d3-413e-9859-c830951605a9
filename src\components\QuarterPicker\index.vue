<template>
  <div>
    <a-popover placement="bottom" overlayClassName="season-picker" trigger="click" v-model="showSeason">
      <template #content>
        <div class="season-picker-box">
          <div class="season-picker-box_header">
            <a-icon title="前一年" class="season-picker-box_header-left" type="double-left" @click="prev"></a-icon>
            <span role="button" class="season-picker-box_header-label">{{ year }}年</span>

            <a-icon type="double-right" title="后一年" @click="next" class="season-picker-box_header-right"></a-icon>
          </div>

          <div class="season-picker_content">
            <table class="el-month-table">
              <tr>
                <td>
                  <div>
                    <a
                      :class="{ choose: seasonValue[0] === year && seasonValue[1] === 1 }"
                      class="cell"
                      @click="selectSeason(1)"
                    >
                      一季度
                    </a>
                  </div>
                </td>
                <td>
                  <div>
                    <a
                      :class="{ choose: seasonValue[0] === year && seasonValue[1] === 2 }"
                      class="cell"
                      @click="selectSeason(2)"
                    >
                      二季度
                    </a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div>
                    <a
                      :class="{ choose: seasonValue[0] === year && seasonValue[1] === 3 }"
                      class="cell"
                      @click="selectSeason(3)"
                    >
                      三季度
                    </a>
                  </div>
                </td>
                <td>
                  <div>
                    <a
                      :class="{ choose: seasonValue[0] === year && seasonValue[1] === 4 }"
                      class="cell"
                      @click="selectSeason(4)"
                    >
                      四季度
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </template>
      <a-input v-model="strValue" readOnly placeholder="请选择季度" />
    </a-popover>
  </div>
</template>

<script>
  const CNNUM = ['一', '二', '三', '四']
  const CHOOSEMONTH = [
    ['01', '02', '03'],
    ['04', '05', '06'],
    ['07', '08', '09'],
    ['10', '11', '12'],
  ]
  // 季度选择器 antd当前版本没有 所以写一个
  export default {
    name: 'QuarterPicker',
    model: {
      prop: 'value',
      event: 'change',
    },
    props: {
      value: {
        type: Array,
        default: () => [],
      },
      defaultValue: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      defaultValue: {
        handler(newVal) {
          // ['2024-07', '2024-09']
          if (newVal && newVal.length) {
            this.dealDate(newVal)
          }
        },
        deep: true,
        immediate: true,
      },
      value: {
        handler(newVal) {
          // ['2024-07', '2024-09']
          if (newVal && newVal.length) {
            this.dealDate(newVal, true)
          }
        },
        deep: true,
        immediate: true,
      },
    },

    data() {
      return {
        showSeason: false,
        year: new Date().getFullYear(),
        strValue: '',
        seasonValue: ['', ''],
      }
    },

    methods: {
      dealDate(date, isValue) {
        this.year = Number(date[0].split('-')[0])
        const month = date[0].split('-')[1]
        CHOOSEMONTH.forEach((item, index) => {
          if (item.indexOf(month) !== -1) {
            this.selectSeason(index + 1, isValue)
          }
        })
      },
      prev() {
        this.year = this.year * 1 - 1
      },
      next() {
        this.year = this.year * 1 + 1
      },
      selectSeason(i, isValue = false) {
        this.strValue = `${this.year}-${CNNUM[i - 1]}季度`
        if (!isValue) {
          // 传入该季度的开始月份和结束月份
          this.$emit('change', [`${this.year}-${CHOOSEMONTH[i - 1][0]}`, `${this.year}-${CHOOSEMONTH[i - 1][2]}`])
        }
        this.seasonValue = [this.year, i]
        this.showSeason = false
      },
    },
  }
</script>
<style lang="less" scoped>
  .season-picker {
    margin: 0;
    padding: 0;
    color: #000000d9;
    font-size: 14px;
    .ant-popover-inner-content {
      overflow: hidden;
      vertical-align: top;
      background: #fff;
      border-radius: 2px;
      box-shadow:
        0 3px 6px -4px #0000001f,
        0 6px 16px #00000014,
        0 9px 28px 8px #0000000d;
      transition: margin 0.3s;
      width: 200px;
      color: #000000d9;
      padding: 0 8px !important;
      .ant-popover-arrow {
        display: none !important;
      }
    }
    &-box {
      &_header {
        user-select: none;
        display: flex;
        height: 34px;
        line-height: 28px;
        color: #000000d9;
        box-sizing: border-box;
        border-bottom: 1px solid #f0f0f0;
        justify-content: space-between;
        margin-bottom: 15px;
        &-left,
        &-right {
          line-height: 28px;
          color: #00000040;
          &:hover {
            color: #000000d9;
          }
        }
      }
      .el-month-table {
        width: 100%;
      }
      .el-month-table td div {
        margin: 10px;
        font-size: 14px;
        .cell {
          user-select: none;
          font-size: 12px;
          color: #333333;
          padding: 8px;
          &:hover {
            color: #40a9ff;
            background: #e6f7ff;
            cursor: pointer;
          }
        }
      }
      .choose {
        color: #fff !important;
        background: #1890ff !important;
      }
    }
  }
</style>
