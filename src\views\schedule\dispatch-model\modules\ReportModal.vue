<template>
  <a-modal
    :visible="visible"
    title="方案报告"
    :width="800"
    :maskClosable="false"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="report-content">
      <div class="report-title">{{ reportData.caseName || '未命名方案' }} - 方案报告</div>
      <div class="report-info">
        <p><strong>方案编码：</strong>{{ reportData.caseCode || '--' }}</p>
        <p><strong>调度模拟场景：</strong>{{ getSceneLabel(reportData.scene) }}</p>
        <p><strong>调度模式：</strong>{{ getDispatchModeLabel(reportData.dispathMode) }}</p>
        <p><strong>预报时段：</strong>{{ reportData.startTime || '--' }} - {{ reportData.endTime || '--' }}</p>
        <p><strong>方案生成时间：</strong>{{ reportData.saveTime || '--' }}</p>
        <p><strong>发起人：</strong>{{ reportData.createdUserName || '--' }}</p>
      </div>
      
      <div class="report-section">
        <h3>调度方案详情</h3>
        <p>这里是方案报告的详细内容，包括调度方案的各项指标、数据分析结果等。</p>
        <p>实际内容将根据具体的业务需求进行展示。</p>
      </div>
    </div>
    
    <div class="report-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleDownload">下载</a-button>
    </div>
  </a-modal>
</template>

<script>
import { dispatchModelOptions, sceneOptions } from '../config'

export default {
  name: 'ReportModal',
  props: {},
  data() {
    return {
      visible: false,
      reportData: {},
      dispatchModelOptions,
      sceneOptions
    }
  },
  methods: {
    handleShow(record) {
      this.visible = true
      this.reportData = record || {}
    },
    handleCancel() {
      this.visible = false
      this.$emit('close')
    },
    handleDownload() {
      this.$message.info('下载功能开发中...')
    },
    getSceneLabel(scene) {
      return this.sceneOptions.find(el => el.value == scene)?.label || '--'
    },
    getDispatchModeLabel(mode) {
      return this.dispatchModelOptions.find(el => el.value == mode)?.label || '--'
    }
  }
}
</script>

<style lang="less" scoped>
.report-content {
  max-height: 500px;
  overflow-y: auto;
  padding: 0 16px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24px;
}

.report-info {
  margin-bottom: 24px;
  
  p {
    margin-bottom: 8px;
  }
}

.report-section {
  margin-bottom: 24px;
  
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
  }
}

.report-footer {
  text-align: right;
  margin-top: 24px;
  
  button {
    margin-left: 8px;
  }
}
</style> 