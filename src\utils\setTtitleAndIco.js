import defaultSettings from '@/config/defaultSettings'

export function handleIcoCreate() {
  let link = document.querySelector("link[rel*='icon']") || document.createElement('link')
  link.type = 'image/x-icon'
  link.rel = 'shortcut icon'

  if (process.env.VUE_APP_FAVICON) {
    link.href = require(`@/assets/ico/${process.env.VUE_APP_FAVICON}.png`)
    document.getElementsByTagName('head')[0].appendChild(link)
  }
}

export function setDocumentTitle(appName) {
  document.title = appName
  defaultSettings.title = appName
  localStorage.setItem('appName', appName)
}
