<template>
  <div id="userLayout" :class="['user-layout-wrapper']">
    <div class="container">
      <router-view />
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'UserLayout',
    mounted() {
      document.body.classList.add('userLayout')
    },
    beforeDestroy() {
      document.body.classList.remove('userLayout')
    }
  }
</script>

<style lang="less" scoped>
  #userLayout.user-layout-wrapper {
    height: 100%;
  }
</style>
