import PropTypes from 'ant-design-vue/es/_util/vue-types'

const ProConfigProviderProps = {
  contentWidth: PropTypes.oneOf(['Fluid', 'Fixed']).def('Fluid'),
  breadcrumbRender: PropTypes.func
}

const ConfigProvider = {
  name: 'ProConfigProvider',
  props: ProConfigProviderProps,
  provide() {
    const _self = this
    return {
      contentWidth: _self.$props.contentWidth,
      breadcrumbRender: _self.$props.breadcrumbRender
    }
  },
  render() {
    const { $scopedSlots } = this
    const children = this.children || $scopedSlots.default
    return children()
  }
}

export default ConfigProvider
