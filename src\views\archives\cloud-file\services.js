import request from '@/utils/request'

// 文件夹树
export function getFolderTree(params) {
  return request({
    url: '/custom/network-disk/folder/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 新增文件夹
export function addFolder(data) {
  return request({
    url: '/custom/network-disk/folder/add',
    method: 'post',
    data,
  })
}

// 修改文件夹
export function updateFolder(data) {
  return request({
    url: '/custom/network-disk/folder/update',
    method: 'post',
    data,
  })
}

// 删除
export function deleteFn(params) {
  return request({
    url: '/custom/network-disk/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 文件列表
export function getList(params) {
  return request({
    url: '/custom/network-disk/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 搜索
export function search(params) {
  return request({
    url: '/custom/network-disk/search',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 新增文件
export function addFile(data) {
  return request({
    url: '/custom/network-disk/file/add',
    method: 'post',
    data,
  })
}
