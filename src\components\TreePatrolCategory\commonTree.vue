<template>
  <div style="height: 100%">
    <a-input placeholder="请输入" @change="onChange" allowClear />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="false"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />
        <template slot="title" slot-scope="node">
          <div class="container">
            <span v-if="node.title.indexOf(searchValue) > -1" class="name">
              {{ node.title.substr(0, node.title.indexOf(searchValue)) }}
              <span style="color: #f50">
                {{ searchValue }}
              </span>
              {{ node.title.substr(node.title.indexOf(searchValue) + searchValue.length) }}
            </span>
            <span v-else class="name" :title="node.title">
              {{ node.title }}
            </span>
            <div class="icon-group">
              <a-icon
                type="plus"
                class="plusType"
                :style="{
                  marginLeft:
                    node.children.length > 0 && node.categoryId == 0
                      ? '40px'
                      : node.children.length > 0 && node.categoryId != 0 && node.parentId == 0
                        ? '22px'
                        : node.children.length == 0 && node.categoryId != 0 && node.parentId == 0
                          ? '22px'
                          : node.children.length == 0 && node.categoryId != 0 && node.parentId != 0
                            ? '4px'
                            : '4px',
                }"
                @click.stop="nodeHandle(node, true)"
              ></a-icon>
              <a-icon
                type="edit"
                v-show="node.categoryId != 0"
                class="editType"
                @click.stop="nodeHandle(node, false)"
              ></a-icon>
              <a-icon
                type="delete"
                v-show="node.categoryId != 0"
                class="deleteType"
                @click.stop="nodeDel(node)"
              ></a-icon>
            </div>
          </div>
        </template>
      </AntTree>
    </div>

    <a-modal
      v-model="visible"
      :title="modalTitle"
      :ok-text="modalOkText"
      :cancel-text="modalCancelText"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model ref="form" :model="form">
        <a-form-item label="名称" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-input v-model="form.categoryName" allowClear />
        </a-form-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'
  import { addBaseCategory, editBaseCategory, getBaseCategory, deleteBaseCategory } from '@/api/common'
  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'TreePatrolCategory',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
      categoryType: {
        type: String,
      },
    },

    data() {
      return {
        loading: false,
        treeData: [],
        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,
        dataList: [],
        visible: false,
        modalType: '',
        modalTitle: '',
        modalOkText: '',
        modalCancelText: '',
        form: {
          categoryId: null,
          categoryName: '',
          parentId: null,
          parentPath: '0',
          patrolType: this.$route.meta.query.patrolType,
        },
        selectedKeys: [],
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    watch: {},
    methods: {
      // 新增/编辑
      nodeHandle(node, type) {
        this.modalType = type
        if (type) {
          this.modalTitle = '新增'
          this.modalOkText = '确定'
          this.form.parentId = node.id
        } else if (!type) {
          this.modalTitle = '编辑'
          this.form.categoryId = node.categoryId
          this.form.categoryName = node.categoryName
          this.form.parentId = node.parentId
          this.modalOkText = '确定'
        }
        this.visible = true
      },
      handleOk() {
        if (this.modalType) {
          let addParam = {
            categoryName: this.form.categoryName,
            parentId: this.form.parentId,
            categoryType: this.$props.categoryType,
          }
          addBaseCategory(addParam).then(res => {
            this.$message.success(`成功`, 3)
            this.getDataSource(undefined, 'search')
            this.form.categoryName = ''
            this.visible = false
          })
        } else if (!this.modalType) {
          let updateParam = {
            categoryId: this.form.categoryId,
            categoryName: this.form.categoryName,
            parentId: this.form.parentId,
            parentPath: '0',
            categoryType: this.$props.categoryType,
          }
          editBaseCategory(updateParam).then(res => {
            this.$message.success(`成功`, 3)
            this.getDataSource(undefined, 'search')
            this.form.categoryName = ''
            this.visible = false
          })
        }
      },

      // 巡检项分类删除
      nodeDel(node) {
        var that = this
        const Ids = node.categoryId
        const names = node.categoryName
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteBaseCategory({ categoryIds: Ids }).then(res => {
              let newArr = that.removeNodeInTree(that.treeData, Ids)
              that.treeData = newArr
              that.$message.success(`成功删除` + res.data + `条数据`, 3)
              that.selectedKeys = []
              that.getDataSource(undefined, 'created')
              that.visible = false
            })
          },
          onCancel() {},
        })
      },
      removeNodeInTree(treeList, id) {
        // 通过id从数组（树结构）中移除元素
        if (!treeList || !treeList.length) {
          return
        }
        for (let i = 0; i < treeList.length; i++) {
          if (treeList[i].id == id) {
            treeList.splice(i, 1)
            break
          }
          this.removeNodeInTree(treeList[i].children, id)
        }
      },
      handleCancel() {
        this.form.categoryName = ''
        this.visible = false
      },
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true

        if (this.treeOptions.dataSource?.length && type != 'search') {
          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)
          // 设置默认选中第一个节点
          if (this.selectedKeys.length == 0) {
            this.selectedKeys = [this.treeData[0].key]
          }
          this.generateList(this.treeData)

          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit('onTreeMounted', this.treeData)
            this.$emit('ok')
          }
        } else {
          const searchInfo = { keywords: value, categoryType: this.$props.categoryType }
          getBaseCategory(searchInfo)
            .then(response => {
              if (this.isLeafDisabled) {
                this.treeData = handleTreeData(response?.data || [])
              }
              this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)
              // 设置默认选中第一个节点
              if (this.selectedKeys.length == 0) {
                this.selectedKeys = [this.treeData[0].key]
              }
              this.generateList(this.treeData)

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })
              this.leafNodes = []
              if (type === 'created') {
                this.$emit('onTreeMounted', response?.data || [])
                this.$emit('ok')
              }
            })
            .catch(res => {
              console.log('error', res)
            })
        }
        this.loading = false
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        if (!keys?.length) return
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree-title {
    position: relative;
  }
  .icon-group {
    position: absolute;
    right: -5px;
    top: 0;
    padding: 6px 3px;
    display: flex;
    background: #f7f8fa;
    opacity: 0;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  ::v-deep .ant-tree > li {
    width: 230px;
    // margin-top: 7px;
  }

  ::v-deep .ant-tree-title {
    width: 230px;
  }

  .tree-panel-tree-box {
    position: relative;
    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
    }
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .plusType,
  .editType,
  .deleteType {
    margin-left: 0 !important;
    padding-right: 6px;
  }
  .deleteType {
    padding-right: 0 !important;
  }
  .container {
    display: flex;
    /* 创建一个弹性容器 */
    justify-content: center;
    /* 水平居中容器内的所有元素 */
    align-items: center;
    /* 垂直居中容器内的所有元素 */
    &:hover {
      .icon-group {
        opacity: 1;
      }
    }
  }

  .name {
    display: inline-block !important;
    width: 90px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
</style>
