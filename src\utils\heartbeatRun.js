export default function heartbeatRun(carryFn, time) {
  if (carryFn()) return
  console.log(carryFn())
  sleep(time)
  console.log(carryFn())
  if (carryFn()) return
  console.log(carryFn())
  sleep(time)
  console.log(carryFn())
  if (carryFn()) return
  console.log(carryFn())
  sleep(time)
  console.log(carryFn())
  if (carryFn()) return

  // let timer = setInterval(()=> {
  //   // if(carryFn()) {
  //   //   timer==null
  //   //   return true;
  //   // }

  //   // return false
  // },time||500)
}

function sleep(time) {
  let b = true
  let timestamp = new Date().getTime()
  while (b) {
    if (new Date().getTime() - timestamp > time) {
      b = false
    } else {
      b = true
    }
  }
}
