<!-- VideoPlayer.vue -->
<template>
  <div class="video-player">
    <!-- 播放控制按钮 -->
    <div class="controls">
      <!-- <a-button @click="togglePlay" shape="circle" size="small" :type="isPlaying ? 'primary' : 'default'">
        <a-icon type="play-circle" />
        {{ isPlaying ? '暂停' : '播放' }}
      </a-button> -->

      <div class="play-button">
        <a-button
          size="small"
          :type="isPlaying ? 'primary' : 'default'"
          :shape="isPlaying ? 'circle' : 'circle'"
          @click="togglePlay"
        >
          <a-icon :type="isPlaying ? 'pause' : 'play-circle'" />
        </a-button>
      </div>
      <!-- 进度条 -->
      <a-progress
        style="margin-top: 3px"
        type="line"
        :percent="progressPercentage"
        :show-info="false"
        :stroke-color="progressColor"
        :format="formatTime"
        @change="onProgressChange"
      />

      <!-- 倍速选择 -->
      <a-select v-model="playbackRate" style="margin-left: 10px">
        <a-select-option value="1">×1</a-select-option>
        <a-select-option value="2">×2</a-select-option>
        <a-select-option value="3">×3</a-select-option>
      </a-select>
    </div>

    <!-- 当前时间和总时间 -->
    <!-- <div class="time-info">{{ currentTime }} / {{ totalTime }}</div> -->
    <div class="time-info">{{ currentTime }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      list: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        isPlaying: false,
        playbackRate: '1',
        progressPercentage: 0,
        currentTime: '0000-00-00 00',
        totalTime: '00:00',
        // list: this.list, // 数据列表
        currentFrameIndex: 0,
        intervalId: null,
        progressColor: '#1890ff', // 进度条颜色
      }
    },
    methods: {
      togglePlay() {
        console.log('togglePlay', this.isPlaying)
        // this.progressPercentage = 0
        if (this.isPlaying) {
          clearInterval(this.intervalId)
        } else {
          if (this.progressPercentage == 100) {
            this.progressPercentage = 0
            this.currentFrameIndex = 0
          }
          this.intervalId = setInterval(() => {
            this.updateProgress()
          }, 1000 / this.playbackRate)
        }
        this.isPlaying = !this.isPlaying
      },
      updateProgress() {
        const newIndex = this.currentFrameIndex + 1
        if (newIndex > this.list.length) {
          clearInterval(this.intervalId)
          this.isPlaying = false
          return
        }
        this.currentFrameIndex = newIndex
        this.progressPercentage = (newIndex / this.list.length) * 100
        // this.progressPercentage = (8 / 12) * 100
        console.log('***88 program', newIndex, this.list)
        this.currentTime = this.list[newIndex].dateTime
        // this.currentTime = this.formatTime(newIndex)
      },
      formatTime(index) {
        const time = this.list[index]
        const minutes = Math.floor(time / 60)
          .toString()
          .padStart(2, '0')
        const seconds = Math.floor(time % 60)
          .toString()
          .padStart(2, '0')
        return `${minutes}:${seconds}`
      },
      onProgressChange(percentage) {
        this.progressPercentage = percentage
        this.currentFrameIndex = Math.round((percentage / 100) * this.list.length)
        this.currentTime = this.formatTime(this.currentFrameIndex)
      },
    },
    mounted() {
      // 初始化数据列表
      this.list = Array.from({ length: 100 }, (_, i) => i * 5) // 示例数据，每帧间隔5秒
      this.totalTime = this.formatTime(this.list[this.list.length - 1])
    },
  }
</script>

<style lang="less" scoped>
  .video-player {
    display: flex;
    flex-direction: column;
    text-align: center;

    .controls {
      display: flex;
    }
  }

  .time-info {
    margin-top: 5px;
    font-size: 14px;
  }

  .play-button {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
  }

  ::v-deep .a-button {
    width: 30px;
    height: 30px;
    background: transparent !important;
    border: none;
    color: white;
    // font-size: 24px;
    border-radius: 50%;
  }

  ::v-deep .a-button:hover {
    background-color: #40a9ff;
  }
</style>
