<template>
  <div class="brief-model">
    <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
      全选
    </a-checkbox>
    <div class="brief-box">
      <div
        v-for="(item, index) in tableData"
        :key="index"
        class="brief-item"
        @click="$emit('onItemClick', item)"
        :style="ids.includes(item.id) ? { background: '#f7f8fa' } : {}"
      >
        <div class="top-operation" :style="ids.includes(item.id) ? { display: 'flex' } : {}">
          <a-checkbox @click.stop :checked="ids.includes(item.id)" @change="onCheckboxChange($event, item)" />

          <div>
            <a-icon
              class="operation-icon"
              type="download"
              :style="{
                'margin-right': '16px',
                visibility: item.isFile === 1 ? 'visible' : 'hidden',
              }"
              @click.stop="$emit('downloadFile', item)"
            />
            <a-icon class="operation-icon" type="delete" @click.stop="$emit('handleDelete', item)" />
          </div>
        </div>

        <div
          :style="{
            width: '88px',
            height: '88px',
            background: `url(${getFileIcon(item)}) no-repeat center / contain`,
          }"
        ></div>

        <TableCellOverflow :content="item.fileName" />

        <div style="font-size: 12px; color: #86909c; margin-top: 6px">
          {{ item.isFile === 1 ? item.sizeHuman : item.updatedTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'BriefModel',
    components: {},
    props: {
      tableData: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      ids: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        indeterminate: false,
        checkAll: false,
      }
    },
    watch: {
      ids: {
        handler(val) {
          if (this.tableData.length === 0) return
          this.indeterminate = !!val.length && val.length < this.tableData.length
          this.checkAll = val.length === this.tableData.length
        },
      },
    },
    computed: {},
    created() {},
    methods: {
      getFileIcon(item) {
        let iconUrl
        if (item.isFile === 0) {
          iconUrl = require('@/assets/images/upload/folder.png')
        } else {
          if (item.fileIcon === 'image') {
            iconUrl = item.fileUrl
          } else {
            iconUrl = require(`@/assets/images/upload/${item.fileIcon}.png`)
          }
        }

        return iconUrl
      },
      onCheckAllChange(e) {
        if (e.target.checked) {
          this.$emit('selectChange', { records: this.tableData.map(el => ({ id: el.id })) })
        } else {
          this.$emit('selectChange', { records: [] })
        }
      },
      onCheckboxChange(e, item) {
        if (e.target.checked) {
          this.$emit('selectChange', { records: this.ids.map(el => ({ id: el })).concat({ id: item.id }) })
        } else {
          this.$emit('selectChange', {
            records: this.ids.filter(el => el !== item.id).map(el => ({ id: el })),
          })
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .brief-model {
    padding: 0 15px 15px 15px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .brief-box {
      margin-top: 15px;
      flex: 1;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(163px, 1fr));
      grid-auto-rows: 208px;
      gap: 24px;
      overflow: auto;
      padding-right: 5px;
    }

    .brief-item {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 40px 14px 14px;
      border-radius: 8px;
      position: relative;

      .top-operation {
        position: absolute;
        justify-content: space-between;
        align-items: center;
        top: 0;
        left: 0;
        right: 0;
        padding: 8px 8px 0 14px;
        display: none;

        .operation-icon {
          cursor: pointer;
          &:hover {
            color: @primary-color;
          }
        }
      }

      &:hover {
        background-color: #fcfcfc;
        // background-color: #f7f8fa;
        .top-operation {
          display: flex;
        }
      }
    }
  }
</style>
