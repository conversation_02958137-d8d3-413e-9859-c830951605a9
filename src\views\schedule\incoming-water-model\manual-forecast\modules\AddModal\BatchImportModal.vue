<template>
  <a-modal
    :title="'批量导入降雨数据'"
    :visible="visible"
    :width="800"
    :maskClosable="false"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="batch-import-container">
      <!-- 操作提示 -->
      <div class="tips-section">
        <a-alert
          message="操作提示"
          description="1. 单击或双击时段降雨量单元格可直接编辑数值；2. 编辑时按回车键移动到下一行，按Tab键切换单元格，按Esc键取消编辑；3. 选择多行区域后可按Ctrl+V粘贴Excel数据；4. 支持拖拽选择多行，按Delete键清空选中数据；5. 点击保存按钮将数据应用到主表格"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <div class="table-wrapper">
          <table class="excel-table" @paste="handlePaste" tabindex="0">
            <thead>
              <tr>
                <th class="time-column">时间</th>
                <th class="rainfall-column">时段降雨量(mm)</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in tableData"
                :key="index"
                :class="{ 'selected-row': isRowSelected(index) }"
              >
                <td class="time-cell">{{ item.time }}</td>
                <td 
                  class="rainfall-cell"
                  :class="{ 'selected': isRainfallCellSelected(index), 'editing': editingCell === index }"
                  @mousedown="handleMouseDown(index, $event)"
                  @mouseover="handleMouseOver(index)"
                  @mouseup="handleMouseUp"
                  @dblclick="startEdit(index)"
                >
                  <div 
                    v-if="editingCell !== index"
                    class="cell-display"
                    @click="startEdit(index)"
                  >
                    {{ item.rainfall || 0 }}
                  </div>
                  <input
                    v-else
                    ref="editInput"
                    v-model.number="item.rainfall"
                    type="number"
                    step="0.1"
                    min="0"
                    class="cell-input"
                    @focus="handleCellFocus(index)"
                    @blur="finishEdit"
                    @keydown="handleInputKeyDown($event, index)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="summary-section">
        <div class="summary-item">
          <span>总时段数：</span>
          <span class="summary-value">{{ tableData.length }}</span>
        </div>
        <div class="summary-item">
          <span>已填充：</span>
          <span class="summary-value">{{ filledCount }}</span>
        </div>
        <div class="summary-item">
          <span>总降雨量：</span>
          <span class="summary-value">{{ totalRainfall.toFixed(1) }} mm</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="footer-actions">
        <a-button @click="handleClear" style="margin-right: 8px;">
          清空数据
        </a-button>
        <a-button @click="handleCancel" style="margin-right: 8px;">
          取消
        </a-button>
        <a-button type="primary" @click="handleSave">
          保存
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'BatchImportModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    timeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      selectedRange: {
        start: -1,
        end: -1
      },
      isSelecting: false,
      focusedCell: -1,
      editingCell: -1
    }
  },
  computed: {
    // 已填充的数据数量
    filledCount() {
      return this.tableData.filter(item => item.rainfall > 0).length
    },
    // 总降雨量
    totalRainfall() {
      return this.tableData.reduce((sum, item) => sum + (item.rainfall || 0), 0)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initTableData()
        this.$nextTick(() => {
          // 让表格获得焦点以支持键盘事件
          const table = this.$el.querySelector('.excel-table')
          if (table) {
            table.focus()
          }
        })
      }
    },
    timeList: {
      handler() {
        if (this.visible) {
          this.initTableData()
        }
      },
      deep: true
    }
  },
  mounted() {
    // 监听全局键盘事件
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    // 初始化表格数据
    initTableData() {
      this.tableData = this.timeList.map(time => ({
        time,
        rainfall: 0
      }))
      this.selectedRange = { start: -1, end: -1 }
    },

    // 处理鼠标按下（开始选择）
    handleMouseDown(index, event) {
      event.preventDefault()
      this.isSelecting = true
      this.selectedRange.start = index
      this.selectedRange.end = index
      
      // 阻止文本选择
      document.addEventListener('selectstart', this.preventSelection)
    },

    // 处理鼠标悬停（拖拽选择）
    handleMouseOver(index) {
      if (this.isSelecting) {
        this.selectedRange.end = index
      }
    },

    // 处理鼠标抬起（结束选择）
    handleMouseUp() {
      this.isSelecting = false
      document.removeEventListener('selectstart', this.preventSelection)
    },

    // 阻止文本选择
    preventSelection(e) {
      e.preventDefault()
      return false
    },

    // 处理单元格获得焦点
    handleCellFocus(index) {
      this.focusedCell = index
      if (this.selectedRange.start === -1) {
        this.selectedRange.start = index
        this.selectedRange.end = index
      }
    },

    // 处理单元格失去焦点
    handleCellBlur() {
      // 延迟清除焦点，避免快速切换时的问题
      setTimeout(() => {
        this.focusedCell = -1
      }, 100)
    },

    // 开始编辑单元格
    startEdit(index) {
      if (this.isSelecting) return
      
      this.editingCell = index
      this.focusedCell = index
      
      // 下一个tick后聚焦到输入框并选中内容
      this.$nextTick(() => {
        const input = this.$refs.editInput
        if (input && input.length) {
          const targetInput = Array.isArray(input) ? input[0] : input
          targetInput.focus()
          targetInput.select()
        }
      })
    },

    // 完成编辑
    finishEdit() {
      this.editingCell = -1
      this.focusedCell = -1
    },

    // 处理输入框键盘事件
    handleInputKeyDown(event, index) {
      switch (event.key) {
        case 'Enter':
          event.preventDefault()
          this.finishEdit()
          // 移动到下一行
          if (index < this.tableData.length - 1) {
            this.startEdit(index + 1)
          }
          break
        case 'Escape':
          event.preventDefault()
          // 恢复原值（可选：这里可以保存原始值来实现真正的取消）
          this.finishEdit()
          break
        case 'Tab':
          event.preventDefault()
          this.finishEdit()
          // Tab移动到下一个可编辑单元格
          if (!event.shiftKey && index < this.tableData.length - 1) {
            this.startEdit(index + 1)
          } else if (event.shiftKey && index > 0) {
            this.startEdit(index - 1)
          }
          break
        case 'ArrowDown':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index < this.tableData.length - 1) {
              this.startEdit(index + 1)
            }
          }
          break
        case 'ArrowUp':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index > 0) {
              this.startEdit(index - 1)
            }
          }
          break
      }
    },

    // 判断行是否被选中
    isRowSelected(index) {
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      return index >= start && index <= end && start !== -1
    },

    // 判断降雨量单元格是否被选中
    isRainfallCellSelected(index) {
      return this.isRowSelected(index)
    },

    // 处理粘贴事件
    handlePaste(event) {
      event.preventDefault()
      
      if (this.selectedRange.start === -1) {
        this.$message.warning('请先选择要粘贴的区域')
        return
      }

      const clipboardData = event.clipboardData || window.clipboardData
      const pastedText = clipboardData.getData('text')
      
      if (!pastedText.trim()) {
        this.$message.warning('粘贴内容为空')
        return
      }

      this.processPastedData(pastedText)
    },

    // 处理粘贴的数据
    processPastedData(text) {
      // 分割行和列
      const lines = text.trim().split(/\r?\n/)
      const values = []
      
      lines.forEach(line => {
        // 处理制表符或多个空格分割的数据
        const cells = line.split(/\t|,|\s+/).filter(cell => cell.trim())
        cells.forEach(cell => {
          const num = parseFloat(cell.trim())
          if (!isNaN(num) && num >= 0) {
            values.push(num)
          }
        })
      })

      if (values.length === 0) {
        this.$message.warning('未识别到有效的数值数据')
        return
      }

      // 获取选中范围
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      const selectedLength = end - start + 1

      // 填充数据到选中区域
      let valueIndex = 0
      for (let i = start; i <= end && i < this.tableData.length; i++) {
        if (valueIndex < values.length) {
          this.tableData[i].rainfall = values[valueIndex]
          valueIndex++
        } else {
          // 如果粘贴的数据不够，循环使用
          this.tableData[i].rainfall = values[valueIndex % values.length]
          valueIndex++
        }
      }

      this.$message.success(`已成功粘贴 ${Math.min(values.length, selectedLength)} 个数据`)
    },

    // 处理键盘事件
    handleKeyDown(event) {
      if (!this.visible) return

      // 如果正在编辑，不处理全局快捷键
      if (this.editingCell !== -1) return

      // Ctrl+V 粘贴
      if (event.ctrlKey && event.key === 'v') {
        // 粘贴事件会由表格的 @paste 处理
        return
      }

      // Ctrl+A 全选
      if (event.ctrlKey && event.key === 'a') {
        event.preventDefault()
        this.selectedRange.start = 0
        this.selectedRange.end = this.tableData.length - 1
        return
      }

      // Delete 或 Backspace 清空选中区域
      if ((event.key === 'Delete' || event.key === 'Backspace') && this.selectedRange.start !== -1) {
        event.preventDefault()
        const start = Math.min(this.selectedRange.start, this.selectedRange.end)
        const end = Math.max(this.selectedRange.start, this.selectedRange.end)
        
        for (let i = start; i <= end; i++) {
          this.tableData[i].rainfall = 0
        }
        return
      }

      // F2 或 Enter 开始编辑选中的单元格
      if ((event.key === 'F2' || event.key === 'Enter') && this.selectedRange.start !== -1 && this.selectedRange.start === this.selectedRange.end) {
        event.preventDefault()
        this.startEdit(this.selectedRange.start)
        return
      }
    },

    // 清空数据
    handleClear() {
      this.$confirm({
        title: '确认清空',
        content: '确定要清空所有降雨量数据吗？',
        onOk: () => {
          this.tableData.forEach(item => {
            item.rainfall = 0
          })
          this.selectedRange = { start: -1, end: -1 }
          this.$message.success('已清空所有数据')
        }
      })
    },

    // 保存数据
    handleSave() {
      // 验证数据
      const hasData = this.tableData.some(item => item.rainfall > 0)
      if (!hasData) {
        this.$message.warning('请先填入降雨量数据')
        return
      }

      // 构造返回数据
      const result = this.tableData.map(item => ({
        time: item.time,
        rainfall: item.rainfall || 0
      }))

      this.$emit('save', result)
      this.handleCancel()
    },

    // 取消/关闭弹窗
    handleCancel() {
      this.$emit('update:visible', false)
      this.selectedRange = { start: -1, end: -1 }
      this.isSelecting = false
      this.focusedCell = -1
      this.editingCell = -1
    }
  }
}
</script>

<style lang="less" scoped>
.batch-import-container {
  .tips-section {
    margin-bottom: 16px;
  }

  .table-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;

    .table-wrapper {
      position: relative;
    }

    .excel-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      outline: none;

      th, td {
        border: 1px solid #e8e8e8;
        padding: 8px 12px;
        text-align: left;
        position: relative;
      }

      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .time-column {
        width: 40%;
        min-width: 150px;
      }

      .rainfall-column {
        width: 60%;
        min-width: 180px;
      }

      .time-cell {
        background-color: #f5f5f5;
        color: #666;
        font-family: 'Consolas', 'Monaco', monospace;
      }

      .rainfall-cell {
        cursor: pointer;
        user-select: none;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background-color: rgba(22, 93, 255, 0.1);
        }

        &.selected {
          background-color: rgba(22, 93, 255, 0.2);
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }

        &.editing {
          background-color: #fff;
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }

        .cell-display {
          width: 100%;
          height: 100%;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          min-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: text;
          
          &:hover {
            background-color: rgba(22, 93, 255, 0.05);
          }
        }

        .cell-input {
          width: 100%;
          border: none;
          outline: none;
          background: transparent;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;
        }
      }

      .selected-row {
        .time-cell {
          background-color: rgba(22, 93, 255, 0.1);
        }
      }

      // 选中区域边框效果
      .rainfall-cell.selected {
        box-shadow: inset 0 0 0 2px #165DFF;
      }
    }
  }

  .summary-section {
    display: flex;
    gap: 24px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;

    .summary-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      
      span:first-child {
        color: #666;
        margin-right: 4px;
      }

      .summary-value {
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
  }
}

// 滚动条样式
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}
</style> 