<template>
  <div class="dropdown-table">
    <!-- :pagination ="{ current: goodsParam.pageNum, size: goodsParam.pageSize, total }" -->
    <a-table
      :columns="columns"
      rowKey="goodsId"
      size="middle"
      :data-source="data"
      ref="table"
      :pagination="false"
      :customRow="customRow"
      class="table"
      :scroll="{ y: 560 }"
    ></a-table>
  </div>
</template>

<script lang="jsx">
  import { getGoodsList } from '../../views/warehouse/warehouse-manager/services'
  import * as _ from 'lodash'

  export default {
    name: 'DropdownTable',
    props: ['changeData', 'goodsName'],
    data() {
      return {
        total: 0,
        selectedOption: '', // 当前选中的选项
        columns: [
          // 表格列定义
          {
            title: '物料名称',
            dataIndex: 'goodsName',
            width: '110px',
            key: 'goodsName'
          },
          {
            title: '物料编码',
            dataIndex: 'goodsCode',
            width: '110px',
            key: 'goodsCode'
          },
          {
            title: '物料型号',
            dataIndex: 'goodsType',
            width: '110px',
            key: 'goodsType'
          },
          {
            title: '物料规格',
            dataIndex: 'goodsSpec',
            width: '110px',
            key: 'goodsSpec'
          },
          {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            align: 'right',
            width: '100px',
            key: 'unitPrice'
          },
          {
            title: '操作',
            width: '60px',
            dataIndex: 'customRow',
            scopedSlots: { customRender: 'customRow' },
            key: 'action',
            customRender: (_, record, i) => {
              return (
                <div>
                  <span
                    style='margin-left:10px;cursor:pointer;color: #1890ff;'
                    onclick={() => {
                      this.handleCheck(record)
                    }}
                  >
                    选择
                  </span>
                </div>
              )
            }
          }
        ],
        goodsParam: {
          goodsName: this.goodsName,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          parentId: 0,
          sort: []
        },
        data: [] // 表格数据源，其中每一行的 type 属性用于判断是否为可选项
      }
    },
    created() {},
    watch: {
      goodsName: {
        handler(newVal, oldVal) {
          this.data = []
          this.goodsName = newVal
          this.getList()
        },
        immediate: true
      }
    },
    methods: {
      getList() {
        // if (!this.goodsName) {
        //   return
        // }
        this.goodsParam = {
          goodsName: this.goodsName,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          parentId: 0,
          sort: []
        }
        getGoodsList(this.goodsParam).then(res => {
          this.data = res.data.data
          this.total = res.data.total
        })
      },
      // 分页
      handleChange(current, pageSize) {
        this.goodsParam.pageNum = current
        this.goodsParam.pageSize = pageSize

        this.getList()
      },
      handleCheck(value) {
        this.childData = value
        this.$emit('changeData', value)
      },
      customRow(record) {
        return {
          props: {
            // xxx... //属性
          },
          on: {
            // 事件
            click: event => {
              this.handleCheck(record)
            }, // 点击行
            dblclick: event => {},
            contextmenu: event => {},
            mouseenter: event => {}, // 鼠标移入行
            mouseleave: event => {}
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .dropdown-table {
    padding-top: 10px;
  }

  /*.table 为全局表格自定义样式*/
  .table .ant-table-body,
  .table .ant-table-header {
    overflow-y: auto !important;
  }
</style>
