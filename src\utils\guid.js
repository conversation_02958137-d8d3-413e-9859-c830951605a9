export default function uuid4() {
  const ho = (n, p) => n.toString(16).padStart(p, '0') /// Return the hexadecimal text representation of number `n`, padded with zeroes to be of length `p`
  const view = new DataView(new ArrayBuffer(16)) /// Create a view backed by a 16-byte buffer
  crypto.getRandomValues(new Uint8Array(view.buffer)) /// Fill the buffer with random data
  view.setUint8(6, (view.getUint8(6) & 0xf) | 0x40) /// Patch the 6th byte to reflect a version 4 UUID
  view.setUint8(8, (view.getUint8(8) & 0x3f) | 0x80) /// Patch the 8th byte to reflect a variant 1 UUID (version 4 UUIDs are)
  return `${ho(view.getUint32(0), 8)}-${ho(view.getUint16(4), 4)}-${ho(view.getUint16(6), 4)}-${ho(
    view.getUint16(8),
    4
  )}-${ho(view.getUint32(10), 8)}${ho(view.getUint16(14), 4)}` /// Compile the canonical textual form from the array data
}
