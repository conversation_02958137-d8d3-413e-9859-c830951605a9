export class UpcWebsocketClient {
  _client = null // 存储WebSocket客户端实例
  _url = ''

  isConnect = false //连接状态
  reConnectNum = 0 //重连次数
  reConnectMaxNum = 3 // 最大重连次数

  // 登录相关
  _logined = false
  _loginTimer = null
  _loginResolve = null

  // 心跳相关
  _heartTimer = null
  _heartBeatResolve = null
  _heartBeatResolveTimer = null

  // 重连句柄
  _autoReloginTimer = null
  _autoReloginDivide = 10 // 每隔 10 S 发起一次重连

  // 实例事件, 需全局订阅
  _instanceEventCallback
  // 主题订阅
  _subIndex = 0
  _subMap = new Map() // Map<topic, Map<subIndex, listner>>

  constructor(url) {
    this._url = url
  }

  onInstanceEvent(fn) {
    if (typeof fn == 'function') {
      this._instanceEventCallback = fn
    }
  }

  emitInstanceEvent(type, data) {
    if (typeof this._instanceEventCallback == 'function') {
      this._instanceEventCallback({ type, data })
    }
  }
  // 根据基础类创建websocket对象
  async createWebsocket() {
    // this._client存储创建出来的websocket对象索引
    if (this._client != null) {
      this._client.dispose()
      this._client = null
    }
    // 拿到websocket连接url,一般都是要携带token的，所以拿url的方法要抽离出类之外，通过构造函数传入
    // const result = await this._urlGenerator()
    // if (result.code != 0) {
    //   return { code: result.code, msg: `获取推送服务认证口令失败` }
    // }
    // this._url = result.data //    `${this._host}/ws?token=${this._token}`;

    return new Promise(resolve => {
      this._loginResolve = resolve
      this._client = new WebSocket(this._url)
      resolve(true)
    })
  }
  // 开始连接，如果连接失败，将尝试自动重连，开启连接
  async start() {
    const connected = await this.createWebsocket()
    // 判断ws对象是否创建成功，否就重连创建，是就开启心跳检测机制
    if (!connected) {
      this.autoRelogin()
    } else {
      this.keepHeartBeat() // 开始心跳
    }
  }
  // 暂停连接，清理相关资源
  pause() {
    // 清理 socket
    this._client && this._client.dispose()
    this._client = null

    // 如果有心跳，取消心跳
    this._heartTimer && clearTimeout(this._heartTimer)
    this._heartTimer = null

    // 如果心跳正在进行, 取消回调
    this._heartBeatResolveTimer && clearTimeout(this._heartBeatResolveTimer)
    this._heartBeatResolveTimer = null
    this._heartBeatResolve = null

    this._loginResolve = null

    // 如果在重连, 去掉重连
    this._autoReloginTimer && clearTimeout(this._autoReloginTimer)
    this._autoReloginTimer = null

    // 清理主题订阅
    this._subIndex = 0
    this._subMap.clear()

    this.isConnect = false //连接状态
    this.reConnectNum = 0

    return
  }
  // 重连,每隔一定时间尝试重新连接
  async autoRelogin() {
    this._autoReloginTimer = setTimeout(async () => {
      this._autoReloginTimer = null
      const ret = await this.createWebsocket()
      if (!ret) {
        return this.autoRelogin()
      } else {
        this.keepHeartBeat() // 开始心跳
      }
      this.isConnect = true
      // 重连成功
      this.emitInstanceEvent(UpcWebsocketEventType.Reconnect, {
        msg: '',
        relogin_divide: this._autoReloginDivide,
      })
    }, this._autoReloginDivide * 1000)
  }
  // 建立连接，连接建立成功之后要做的事情，基础类的onopen事件回调函数
  onConnect() {
    console.log(`webSocket已连接`)
    this.isConnect = true
    this.reConnectNum = 0
    typeof this._loginResolve == 'function' && this._loginResolve(true)
    this._loginResolve = null
    this.recoverSubscribe() // 恢复订阅
  }
  // WebSocket连接断开时调用，清理资源，基础类的onclose事件回调函数
  onDisconnect(e) {
    console.log(`webSocket已经关闭 ${e} `)
    // 清理 socket
    this._client && this._client.dispose()
    this._client = null

    // 如果有心跳，取消心跳
    this._heartTimer && clearTimeout(this._heartTimer)
    this._heartTimer = null
    // 如果心跳正在进行, 取消回调
    this._heartBeatResolveTimer && clearTimeout(this._heartBeatResolveTimer)
    this._heartBeatResolveTimer = null
    this._heartBeatResolve = null

    // 触发掉线通知掉线
    this.emitInstanceEvent(UpcWebsocketEventType.Disconnect, {
      msg: '',
      relogin_divide: this._autoReloginDivide,
    })

    //被动断开，重新连接
    if (e && e?.code) {
      this.autoRelogin()
      console.log('websocket连接不上，请重新登录或联系开发人员!')
    }
  }
  // 接收到的 WebSocket消息，解析消息并调用相应的处理函数，基础类的onmessage事件回调函数
  onMessage(message) {
    const msg = JSON.parse(message)
    // console.log("onMessage", msg);
    if (!msg.channel) {
      console.error('onMessage error ')
      return
    }
    if (!(typeof msg.text == 'string' && JSON.parse(msg.text).action_ans == 'subscriber successed!')) {
      this.handlePublish(msg)
    }
  }

  // 使用nginx代理webSocket链接，客户端和服务器握手成功后，如果在60s时间内没有数据交互，连接就会自动断开
  // 心跳检测，定期 30s 发送心跳请求并处理心跳响应
  keepHeartBeat() {
    this._heartTimer = setTimeout(async () => {
      this._heartTimer = null
      const ret = await this.sendHeartBeat()
      // if (ret?.code == 1) {
      //   return this.onDisconnect();
      // }
      return this.keepHeartBeat()
    }, 30 * 1000) //
  }
  // 发送心跳请求，返回一个Promise，用于处理心跳响应
  sendHeartBeat() {
    return new Promise(resolve => {
      this._heartBeatResolve = resolve
      this._heartBeatResolveTimer = setTimeout(() => {
        this._heartBeatResolveTimer = null
        if (this._heartBeatResolve !== null) {
          this._heartBeatResolve({ code: 100, ret_msg: 'sendHeartBeat timeout', data: null })
          this._heartBeatResolve = null
        }
      }, 10 * 1000)
      const reqData = {
        action: 'req',
        channel: 'ping',
      }
      this._client && this._client.sendMessage(JSON.stringify(reqData))
    })
  }
  //
  handlePublish(msg) {
    const topic = msg.channel
    const handleMap = this._subMap.get(topic)
    if (handleMap != null) {
      handleMap.forEach(fn => {
        try {
          fn(JSON.parse(msg.text))
        } catch (e) {
          console.log(e)
        }
      })
    }
  }
  // 恢复所有主题的订阅
  recoverSubscribe() {
    this._subMap.forEach((handleMap, topic) => {
      if (handleMap != null && handleMap.size > 0) {
        const reqData = {
          action: 'sub',
          channel: topic,
          data: {},
        }
        this._client && this._client.sendMessage(JSON.stringify(reqData))
      }
    })
  }
  /**
   * 订阅指定主题，并注册事件监听器。
   * @param topic 订阅的主题字符串。
   * @param listener 当订阅主题触发时执行的回调函数。
   * @returns 返回一个句柄，用于取消订阅。
   */
  subscribe(topic, listener) {
    if (typeof listener != 'function') return
    let handleMap = this._subMap.get(topic)
    if (handleMap == null) {
      handleMap = new Map()
      this._subMap.set(topic, handleMap)
    }
    const handle = ++this._subIndex
    handleMap.set(handle, listener)
    if (handleMap.size == 1) {
      // 初始添加订阅, 需要向后台发送消息
      const reqData = {
        action: 'sub',
        channel: topic,
        data: {},
      }
      this._client && this._client.sendMessage(JSON.stringify(reqData))
    }
    return handle
  }
  /**
   * 取消订阅指定主题或删除指定句柄对应的监听器。
   * @param topic 要取消订阅的主题字符串。
   * @param handle 可选，取消特定句柄对应的监听器；若未提供，则取消该主题下的所有监听器。
   */
  unsubscribe(topic, handle) {
    const handleMap = this._subMap.get(topic) // 获取对应主题的订阅
    if (handleMap == null || handleMap.size == 0) {
      // 未订阅，无需退订
      return
    }
    if (typeof handle == 'undefined') {
      // 全部退订
      handleMap.clear()
    } else {
      handleMap.delete(handle)
    }
    if (handleMap.size == 0) {
      // 向后台取消订阅
      const reqData = {
        action: 'unsub',
        channel: topic,
        data: {},
      }
      this._client && this._client.sendMessage(JSON.stringify(reqData))
    }
  }
  /**
   * 清理资源并释放所有内部状态。
   */
  dispose() {
    // 清理 socket
    this._client && this._client.dispose()
    this._client = null

    // 如果有心跳，取消心跳
    this._heartTimer && clearTimeout(this._heartTimer)
    this._heartTimer = null

    // 如果心跳正在进行, 取消回调
    this._heartBeatResolveTimer && clearTimeout(this._heartBeatResolveTimer)
    this._heartBeatResolveTimer = null
    this._heartBeatResolve = null

    // 如果在重连, 去掉重连
    this._autoReloginTimer && clearTimeout(this._autoReloginTimer)
    this._autoReloginTimer = null

    // 清理系统订阅
    this._instanceEventCallback = null
    // 清理主题订阅
    this._subIndex = 0
    this._subMap.clear()
  }
}
