<template>
  <div>
    <iframe v-if="wordType == '2'" :src="'https://view.officeapps.live.com/op/view.aspx?src=' + wordUrl" width='830px' height='730px' frameborder='1'></iframe>
    <vue-office-docx v-if="wordType == '1'" :src="wordUrl"/>
  </div>
</template>

<script lang="jsx">
import { getValueByKey } from '@/api/common'
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'

export default {
  components: {
    VueOfficeDocx
  },
  props: ['wordUrl'],
  data(){
    return {
      wordType: null,
      // internetSrc: 'https://sthzxgq.oss-cn-hangzhou.aliyuncs.com/tmp/2023%E5%B9%B4%E7%AC%AC1%E6%9C%9F%E5%98%89%E5%85%B4%E5%B8%82%E7%A7%80%E6%B4%B2%E5%8C%BA%E6%B0%B4%E9%9B%A8%E6%83%85%E7%AE%80%E6%8A%A5%20%281%29.docx'
    }
  },
  created() {
    getValueByKey('docReviewComponentType').then(res => {
      if (res.code == 200) {
        this.wordType = res.data
      }
    })
  }
}
</script>