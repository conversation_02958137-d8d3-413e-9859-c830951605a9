@import "../../../../assets/styles/default.less";

@ant-pro-setting-drawer: ~'@{ant-prefix}-pro-setting-drawer';

.@{ant-pro-setting-drawer} {
  &-content {
    position: relative;
    min-height: 100%;

    .ant-list-item {
      span {
        flex: 1;
      }
    }
  }

  &-block-checbox {
    display: flex;

    &-item {
      position: relative;
      margin-right: 16px;
      // box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      cursor: pointer;

      img {
        width: 48px;
      }
    }

    &-selectIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      padding-top: 15px;
      padding-left: 24px;
      color: @primary-color;
      font-weight: bold;
      font-size: 14px;

      .action {
        color: @primary-color;
      }
    }
  }

  &-color_block {
    display: inline-block;
    width: 38px;
    height: 22px;
    margin: 4px;
    margin-right: 12px;
    vertical-align: middle;
    border-radius: 4px;
    cursor: pointer;
  }

  &-title {
    margin-bottom: 12px;
    color: @heading-color;
    font-size: 14px;
    line-height: 22px;
  }

  &-handle {
    position: absolute;
    top: 240px;
    right: 300px;
    z-index: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    font-size: 16px;
    text-align: center;
    background: @primary-color;
    border-radius: 4px 0 0 4px;
    cursor: pointer;
    pointer-events: auto;
  }

  &-production-hint {
    margin-top: 16px;
    font-size: 12px;
  }
}
