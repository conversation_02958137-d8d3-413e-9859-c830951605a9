<template>
  <div>
    <a-input @click="showSelectUser" v-model="showValue" select-model="single" readOnly>
      <a-icon slot="suffix" type="user-add" />
    </a-input>
    <ant-modal
      :visible="open"
      :modal-title="title"
      :adjust-size="false"
      @cancel="cancel"
      modalHeight="480"
      modalWidth="800"
      dialogClass="personSelect"
    >
      <a-row slot="content">
        <a-spin :spinning="spinning" :delay="delayTime" tip="Loading...">
          <a-col class="treeBox treeborder" :span="24">
            <a-input-search placeholder="请输入用户信息" v-model="searchUser" @search="filterNode" />
            <div class="personSelectTree">
              <a-tree
                v-if="selectModel == 'multi'"
                v-model="checkedKeys"
                checkable
                :replaceFields="replaceFields"
                :default-expanded-keys="expandedKeys"
                :expanded-keys="expandedKeys"
                :auto-expand-parent="autoExpandParent"
                :selected-keys="selectedKeys"
                :tree-data="deptOptions"
                :load-data="onLoadData"
                showLine
                showIcon
                :loading="loading"
                @expand="onExpand"
                @check="checkNode"
              >
                <SvgIcon slot="org" iconClass="companyFill" class="depIcon" />
                <SvgIcon slot="company" iconClass="company" class="depIcon" />
                <SvgIcon slot="dept" iconClass="connections" class="depIcon" />
                <a-icon slot="user" type="user" class="depIcon" />
                <template slot="title" slot-scope="{ title, attributes }">
                  <span v-if="title.indexOf(searchValue) > -1">
                    {{ title.substr(0, title.indexOf(searchValue)) }}
                    <span style="color: #f50">{{ searchValue }}</span>
                    {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
                  </span>
                  <span v-else-if="attributes.deptPinyin.indexOf(searchValue) > -1">
                    <span style="color: #f50">{{ title }}</span>
                  </span>
                  <span v-else>{{ title }}</span>
                </template>
              </a-tree>
              <a-tree
                v-if="selectModel == 'single'"
                :replaceFields="replaceFields"
                :default-expanded-keys="expandedKeys"
                :expanded-keys="expandedKeys"
                :auto-expand-parent="autoExpandParent"
                :selected-keys="selectedKeys"
                :tree-data="deptOptions"
                :load-data="onLoadData"
                showLine
                showIcon
                @expand="onExpand"
                @select="selectNode"
              >
                <SvgIcon slot="org" iconClass="companyFill" class="depIcon" />
                <SvgIcon slot="company" iconClass="company" class="depIcon" />
                <SvgIcon slot="dept" iconClass="connections" class="depIcon" />
                <a-icon slot="user" type="user" class="depIcon" />
                <template slot="title" slot-scope="{ title, attributes }">
                  <span v-if="title.indexOf(searchValue) > -1">
                    {{ title.substr(0, title.indexOf(searchValue)) }}
                    <span style="color: #f50">{{ searchValue }}</span>
                    {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
                  </span>
                  <span v-else-if="attributes.deptPinyin.indexOf(searchValue) > -1">
                    <span style="color: #f50">{{ title }}</span>
                  </span>
                  <span v-else>{{ title }}</span>
                </template>
              </a-tree>
            </div>
          </a-col>
        </a-spin>
      </a-row>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="saveSelectUser">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import { dingUser } from '@/api/user'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Sortable from 'sortablejs' // 列交换第三方插件
  export default {
    props: {
      title: {
        type: String,
        default: '人员选择'
      },
      // 默认值
      defaultValue: {
        required: false,
        default: null
      },
      // 返回数据
      value: {
        required: false
      },
      // 单选 single ，多选 multi
      selectModel: {
        type: String,
        required: false,
        default: 'single'
      },
      maxSelect: {
        type: Number,
        required: false,
        default: 0
      }
    },
    data() {
      return {
        showValue: '',
        oldValue: '',
        spinning: false,
        delayTime: 200,
        searchUser: '',
        sortable: undefined,
        replaceFields: {
          children: 'children',
          key: 'id',
          value: 'id',
          title: 'name'
        },
        loading: false,
        open: false,
        expandedKeys: [],
        autoExpandParent: true,
        checkedKeys: [],
        selectedKeys: [], // 左侧树所有选中节点
        deptOptions: [],
        deptNodes: [],
        oldOptions: [],
        oldDeptOptions: [], // 记录查询前数据结构
        oldExpandedKeys: [],
        expandSonData: [], // 异步展开节点时记录子节点
        select: {
          ids: '',
          names: ''
        }, // 最终选择用户对象
        searchValue: '',
        userdata: [],
        selectCount: 0
      }
    },
    components: {
      AntModal
    },
    created() {
      this.getTreeselect()
    },
    mounted() {},
    watch: {
      checkedKeys(val) {
        /* if (val.length > 1) {
        this.$message.warning(`已设置最多选择1人`);
        return;
      } */
      },
      userdata: {
        immediate: true,
        handler(val) {
          this.selectCount = val.length
          // if (this.selectModel === "multi" &&val.length > 0 &&this.sortable === undefined) {
          // }
        }
      },
      select: {
        immediate: true,
        handler(val) {
          this.oldValue = this.select && this.select.names ? this.select.names : ''
          this.showValue = this.oldValue
        }
      },
      value: {
        immediate: true,
        handler(newV) {
          if (newV) {
            this.select = newV
          } else {
            this.select = {
              ids: '',
              names: ''
            }
          }
        }
      }
    },
    methods: {
      resetSelectUserInfo() {
        this.checkedKeys = []
        this.userdata = []
      },
      /** 查询部门下拉树结构 */
      async getTreeselect() {
        this.loading = true
        await dingUser().then(response => {
          this.oldOptions = this.changeIconState(response.data)
          this.deptOptions = this.changeIconState(response.data)
          this.getExpandedKeys(this.deptOptions, 3)
          Object.assign(this, {
            expandedKeys: this.expandedKeys,
            searchValue: '',
            autoExpandParent: true
          })
          this.loading = false
        })
      },
      //icon
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = { icon: data[i].type }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      getExpandedKeys(nodes, expandLevel) {
        // 递归展开指定层级
        if (expandLevel > 1) {
          // 最后一层不展开
          nodes.forEach(node => {
            this.expandedKeys.push(node.id)
            expandLevel = expandLevel - 1
            return this.getExpandedKeys(node.children, expandLevel)
          })
        }
      },
      getExpandedAllKeys(nodes) {
        // 递归展开所有层
        if (!nodes || nodes.length === 0) {
          return []
        }
        // 最后一层不展开
        nodes.forEach(node => {
          this.deptNodes.push(node.id)
          return this.getExpandedAllKeys(node.children)
        })
      },
      onLoadData(treeNode) {
        // 展开节点时动态加载数据
        return new Promise(resolve => {
          if (treeNode.dataRef.children) {
            resolve()
            return
          }
          this.spinning = !this.spinning
          dingUser().then(response => {
            treeNode.dataRef.children = response.data
            this.expandSonData = response.data
            // if (treeNode.checked) {
            //   // 当前节点已经是选中状态时异步加载子节点的话，需要将子节点人员选择到已选人员列表
            //   this.setSelectUserInfoByNodes(response.data)
            // } else {
            //   this.checkedKeys = this.selectedKeys
            // }
            this.checkedKeys = this.selectedKeys
            this.spinning = !this.spinning
            resolve()
          })
        })
      },
      showSelectUser() {
        this.open = true
        this.resetSelectUserInfo()
      },
      /*
       * arr:树形结构数组
       *keyword:搜索关键词
       */
      filterTree(arr, keyword) {
        let emptyArr = []
        for (let item of arr) {
          if (item.name.includes(keyword) && item.type == 'user') {
            if (item.children && Array.isArray(item.children) && item.children.length > 0) {
              item.children = this.filterTree(item.children, keyword)
            }
            emptyArr.push(item)
          } else if (item.children && Array.isArray(item.children) && item.children.length > 0) {
            item.children = this.filterTree(item.children, keyword)
            if (item.children.length) {
              emptyArr.push(item)
            }
          }
        }
        return emptyArr
      },
      async filterNode() {
        let newOptions = []
        // this.deptOptions = [];
        if (this.oldDeptOptions.length === 0) {
          this.oldDeptOptions = this.deptOptions
          this.oldExpandedKeys = this.expandedKeys
        }
        if (this.searchUser.trim() == '') {
          // 触发父页面设置树数据
          this.getTreeselect()
          this.deptOptions = this.oldDeptOptions
          Object.assign(this, {
            expandedKeys: this.oldExpandedKeys,
            searchValue: this.searchUser,
            autoExpandParent: true
          })
        } else if (this.searchUser.trim() != '') {
          this.deptNodes = []
          this.selectedKeys = []
          this.checkedKeys = []
          await dingUser().then(response => {
            // let data = this.findIndexArray(response.data,[],[]);
            this.oldDeptOptions = this.changeIconState(response.data, [])
          })
          const searchInfo = {
            searchText: this.searchUser
          }
          newOptions = searchInfo.searchText
            ? this.filterTree(this.oldDeptOptions, searchInfo.searchText)
            : this.oldOptions
          this.deptOptions = newOptions
          this.getExpandedAllKeys(newOptions) // 递归展开所有层
          Object.assign(this, {
            expandedKeys: this.deptNodes,
            searchValue: this.searchUser,
            autoExpandParent: true
          })
          this.deptNodes = []
        }
      },
      callback(key) {
        // console.log(key);
      },
      cancel(e) {
        this.searchUser = ''
        this.getTreeselect()
        this.$emit('close')
        this.open = false
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
      onCheck(checkedKeys) {
        this.checkedKeys = checkedKeys
      },
      selectNode(selectedKeys, e) {
        // 单选树节点触发
        var nodeData = e.node.dataRef
        const deptType = nodeData.attributes.deptType
        if (deptType === 'user') {
          this.selectedKeys = []
          this.userdata = []
          const id = nodeData.key
          const name = nodeData.title
          const parentIds = nodeData.parentIds
          const subtitle = nodeData.attributes.subtitle
          const selectUser = {
            id: id,
            name: name,
            subtitle: subtitle,
            parentIds: parentIds,
            icon: 'user'
          }
          this.selectedKeys.push(id)
          this.userdata.push(selectUser)
        } else {
          this.$message.warning('请选择用户添加')
        }
      },
      checkNode(selectedKeys, e) {
        if (e.checked) {
          if (this.checkedKeys.length > 1) {
            this.$message.warning(`已设置最多选择1人`)
            return
          } else {
            this.setSelectUserInfo(e.checkedNodes)
          }
        } else {
          // 移除当前选中节点及其子节点数据
          this.removeSelectUserByUserTree(e.node, 'node')
        }
      },
      unique(arr) {
        // 数据去重
        const res = new Map()
        return arr.filter(arr => !res.has(arr.id) && res.set(arr.id, 1))
      },
      removeSelectUserByUserTree(node, dataSource) {
        let id = ''
        let childrens = null
        if (dataSource == 'node') {
          id = node.dataRef.id
          childrens = node.dataRef.children
        } else {
          id = node.id
          childrens = node.children
        }

        this.selectedKeys = this.selectedKeys.filter(function (item) {
          return item != id
        })
        this.checkedKeys = this.checkedKeys.filter(function (item) {
          return item != id
        })
        this.userdata = this.userdata.filter(function (item) {
          return item.id != id
        })

        if (childrens !== null) {
          childrens.forEach(childrenNode => {
            this.removeSelectUserByUserTree(childrenNode, 'children')
          })
        }
      },
      setSelectUserInfo(checkedNodes) {
        // 过滤掉部门数据
        checkedNodes.forEach(node => {
          const name = node.componentOptions.propsData.dataRef.name
          const id = node.componentOptions.propsData.dataRef.id
          const parentIds = node.componentOptions.propsData.dataRef.parentId
          const deptType = node.componentOptions.propsData.dataRef.type
          const subtitle = ''
          const unionId = node.componentOptions.propsData.dataRef.ext.unionId
          const userId = node.componentOptions.propsData.dataRef.ext.userId
          this.setSelectEdUserInfo(id, name, subtitle, deptType, parentIds, unionId, userId)
        })
      },
      filterUserInfo(nodes, id) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].key == id) {
            return nodes[i]
          } else {
            if (nodes[i].children && nodes[i].children.length > 0) {
              let res = this.filterUserInfo(nodes[i].children, id)
              if (res) {
                return res
              }
            }
          }
        }
        return null
      },
      setSelectEdUserInfo(id, name, subtitle, type, parentIds, unionId, userId) {
        this.selectedKeys.push(id)
        this.checkedKeys.push(id)
        if (type == 'user') {
          const selectUser = {
            id: id,
            name: name,
            subtitle: subtitle,
            parentIds: parentIds,
            icon: 'user',
            unionId: unionId, //userTmpData.ext.unionId,
            userId: userId //userTmpData.ext.userId,
          }

          this.userdata.push(selectUser)
          this.userdata = this.unique(this.userdata)
        }
      },
      saveSelectUser() {
        // 保存选中数据
        let userIds = ''
        let unionIds = ''
        let names = ''
        if (this.userdata.length > 1) {
          this.$message.warning(`已设置最多选择1人！`)
          return
        }
        this.userdata.forEach(function (node, index) {
          userIds = node.userId
          unionIds = node.unionId
          names = node.name
        })
        // console.log(this.userdata);
        this.showValue = names
        const result = {
          userIds,
          unionIds
        }
        this.$emit('change', result)
        this.$nextTick(() => {
          this.select = result
          // 双向绑定
          this.$emit('input', result)
          this.$emit('callBack', result)
        })
        this.searchUser = ''
        this.getTreeselect()
        this.open = false
      }
    }
  }
</script>
<style lang="less">
  .ant-tree-checkbox-disabled {
  }

  body .ant-tree li .ant-tree-node-content-wrapper .depIcon {
    color: #666666;
    font-size: 20px;
  }

  .personSelect .ant-modal-body {
    padding: 0;
  }

  .personSelectTree {
    height: 325px;
    overflow: auto;
    padding-left: 15px;
  }

  .personSelect {
    .ant-tabs-bar {
      margin: 0;
    }

    .treeborder {
      border-right: 1px solid #e8e8e8;
    }

    .treeBox {
      padding: 0;

      .ant-input-search {
        width: 100%;
        padding: 10px 15px 5px;
      }

      .ant-input-affix-wrapper .ant-input-suffix {
        right: 22px;
      }

      .ant-tree-checkbox {
        padding: 8px 0 0;
      }

      .ant-tree-checkbox-checked::after {
        border: none;
      }

      .ant-tree li .ant-tree-node-content-wrapper {
        width: calc(100% - 40px);
      }
    }

    .contentBox {
      padding: 0;

      .ant-list-items {
        margin: 0 10px;
      }

      .ant-avatar {
        width: 30px;
        height: 30px;
        line-height: 30px;
        background: #47b5e6;
      }

      .ant-checkbox-wrapper {
        font-size: 12px;
      }

      .ant-checkbox-group {
        display: block;
        height: 330px;
        overflow: auto;
      }

      .ant-list-item-meta-avatar {
        margin-right: 0;
      }

      .ant-list-item-meta-title {
        line-height: 30px;
        font-size: 12px;
        margin-bottom: 0px;
      }

      .ant-avatar.ant-avatar-icon {
        margin: 0 10px;
      }

      .ant-list-item-action > li {
        padding: 0 5px;
      }

      .ant-list-split .ant-list-item {
        border-bottom: 0;
        padding: 5px 10px;

        .ant-list-item-action {
          display: none;
          margin-left: 0;
        }

        .title-name {
          color: #323232;
          margin-right: 5px;
        }

        .title-dept {
          color: #a5a5a5;
        }
      }

      .ant-list-item:hover {
        background: #f0f6ff;
        cursor: move;

        .ant-list-item-action {
          display: block;
        }
      }

      .select-list-color {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        z-index: 9999;
      }

      .ant-list-item-action .ant-list-item-action-split {
        width: 0;
      }
    }

    .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
      margin-right: 0px;
      margin-top: 0px;
      height: 40px;
      line-height: 40px;
      border: 0;
      border-right: 1px solid #fff;
      background: #f3f3f3;
      border-radius: 0;
    }

    .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
      background: #fff;
    }

    .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-wrap {
      padding: 0 10px;
      background: #f3f3f3;
    }
  }
</style>
