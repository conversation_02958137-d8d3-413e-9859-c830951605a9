import request from '@/utils/request'

export function ssoLogin(parameter) {
  return request({
    url: '/sys/casLogin',
    method: 'post',
    data: parameter
  })
}

export function getInfo(parameter) {
  return request({
    url: '/sys/user/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: 'userId=' + parameter
  })
}

export function logout() {
  return request({
    url: '/sys/logout',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 钉钉企业通讯录
export function dingUser() {
  return request({
    url: '/external/dingtalk/addressBook',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取登录用户菜单权限
export function getMenuPermissions() {
  return request({
    url: '/sys/user/getMenuPermissions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取当前登录用户信息
export function getUserProfile() {
  return request({
    url: '/sys/user/profile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
