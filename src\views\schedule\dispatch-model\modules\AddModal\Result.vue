<template>
  <div style="flex: 1; display: flex">
    <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-spin></a-spin>
    </div>

    <div
      v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
    >
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <div v-else-if="!!resultData" style="height: 100%; width: 100%; display: flex">
      <!-- 左侧统计信息 -->
      <div class="left" style="width: 270px; padding: 14px; border-right: 1px solid #f0f0f0">
        <div class="item">
          <div class="label">调度方案编号</div>
          <div class="value">{{ resultData.dispatchCode }}</div>
        </div>
        <div class="item">
          <div class="label">调度方案名称</div>
          <div class="value">{{ resultData.dispatchPlanName }}</div>
        </div>
        <div class="item">
          <div class="label">开始时间</div>
          <div class="value">{{ resultData.startTime }}</div>
        </div>
        <div class="item">
          <div class="label">结束时间</div>
          <div class="value">{{ resultData.endTime }}</div>
        </div>
        <div class="item">
          <div class="label">累计降雨量:</div>
          <div class="value">{{ resultData.totalRainfall }}mm</div>
        </div>
        <div class="item">
          <div class="label">起调水位:</div>
          <div class="value">{{ resultData.startWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">末期水位:</div>
          <div class="value">{{ resultData.endWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最高水位</div>
          <div class="value">{{ resultData.maxWaterLevel }}m</div>
        </div>
        <div class="item">
          <div class="label">最低水位</div>
          <div class="value">{{ resultData.minWaterLevel }}m</div>
        </div>  
        <div class="item">
          <div class="label">累计入库水量:</div>
          <div class="value">{{ resultData.totalInflowVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计出库水量:</div>
          <div class="value">{{ resultData.totalOutflowVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计供水量:</div>
          <div class="value">{{ resultData.totalSupplyVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">累计泄洪量:</div>
          <div class="value">{{ resultData.totalFloodVolume }}万m³</div>
        </div>
        <div class="item">
          <div class="label">洪峰流量:</div>
          <div class="value">{{ resultData.peakFlow }}m³/s</div>
        </div>
        <div class="item">
          <div class="label">峰现时间:</div>
          <div class="value">{{ resultData.peakTime }}</div>
        </div>
      </div>

      <!-- 右侧图表和表格 -->
      <div style="flex: 1; height: 100%">
        <div style="height: 50%">
          <BarAndLineMixChart :dataSource="chartData" />
        </div>

        <ResultTable :dataSource="resultData?.resvrDispResList || []" :resultData="resultData" style="height: 50%" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  // import { forecast, getResvrDisp } from '../../services'
  // import { SocketClient } from '@/utils/sockClient.js'
  import BarAndLineMixChart from './BarAndLineMixChart.vue'
  import ResultTable from './ResultTable.vue'
  import moment from 'moment'

  export default {
    name: 'Result',
    props: ['baseInfo', 'outflowData'],
    components: {
      BarAndLineMixChart,
      ResultTable,
    },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        resultData: null,
        chartData: [],
      }
    },
    computed: {},
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null

      // 模拟计算过程
      setTimeout(() => {
        this.generateMockResult()
        this.$emit('update:isDisabledBtn', false)
        this.loading = false
      }, 2000)

      // 注释掉原有接口调用
      // forecast({ ...this.baseInfo }).then(res => {
      //   getResvrDisp({ resvrDispId: res.data }).then(resp => {
      //     this.resultData = resp.data
      //     // ... 原有逻辑
      //   })
      // })
    },
    mounted() {},
    beforeDestroy() {
      // this.socketIns.disconnect()
    },
    methods: {
      generateMockResult() {
        // 生成模拟结果数据
        const startTime = moment(this.baseInfo.startTime)
        const endTime = moment(this.baseInfo.endTime)
        const duration = endTime.diff(startTime, 'hours')
        
        const resvrDispResList = []
        let totalRainfall = 0
        let totalInflowVolume = 0
        let totalSupplyVolume = 0
        let totalFloodVolume = 0
        let peakFlow = 0
        let peakTime = ''
        
        for (let i = 0; i <= duration; i++) {
          const time = startTime.clone().add(i, 'hours').format('YYYY-MM-DD HH:mm')
          // 使用从OutflowProcess传递过来的降雨数据，如果没有则使用随机数据
          const rain = this.outflowData?.rainfallData?.[i]?.rainfall || (Math.random() * 10) // 0-10mm
          const inflow = 100 + Math.random() * 50 // 100-150
          const supplyFlow = this.outflowData?.tableData?.[i]?.supplyFlow || (80 + Math.random() * 40)
          const floodFlow = this.outflowData?.tableData?.[i]?.floodFlow || (40 + Math.random() * 30)
          const waterLevel = 85 + Math.random() * 5 // 85-90
          
          totalRainfall += rain
          totalInflowVolume += inflow * 0.0036 // 转换为万m³
          totalSupplyVolume += supplyFlow * 0.0036
          totalFloodVolume += floodFlow * 0.0036
          
          if (inflow > peakFlow) {
            peakFlow = inflow
            peakTime = time
          }
          
          resvrDispResList.push({
            tm: time,
            rain: rain.toFixed(1),
            inflow: inflow.toFixed(1),
            supplyFlow: supplyFlow.toFixed(1),
            floodFlow: floodFlow.toFixed(1),
            wlv: waterLevel.toFixed(2),
            supplyVolume: (supplyFlow * 0.0036).toFixed(2),
            floodVolume: (floodFlow * 0.0036).toFixed(2),
          })
        }
        
        this.resultData = {
          caseCode: 'DISP_' + Date.now(),
          caseName: this.baseInfo.caseName,
          startTime: this.baseInfo.startTime,
          endTime: this.baseInfo.endTime,
          startWaterLevel: (85 + Math.random() * 2).toFixed(2),
          endWaterLevel: (87 + Math.random() * 2).toFixed(2),
          maxWaterLevel: (89 + Math.random() * 2).toFixed(2),
          minWaterLevel: (83 + Math.random() * 2).toFixed(2),
          totalRainfall: (this.outflowData?.totalRainfall || totalRainfall).toFixed(1),
          totalInflowVolume: totalInflowVolume.toFixed(1),
          totalOutflowVolume: (totalSupplyVolume + totalFloodVolume).toFixed(1),
          totalSupplyVolume: totalSupplyVolume.toFixed(1),
          totalFloodVolume: totalFloodVolume.toFixed(1),
          peakFlow: peakFlow.toFixed(1),
          peakTime,
          resvrDispResList,
        }
        
        // 处理图表数据
        const data = this.resultData.resvrDispResList
        
        const rainData = {
          name: '时段雨量',
          data: data.map(el => [el.tm, el.rain]),
        }
        const sumRainData = {
          name: '累计降雨量',
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + parseFloat(b[1]), 0)
            return [el[0], +sum.toFixed(1)]
          }),
        }
        this.chartData = [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: data.map(el => [el.tm, el.wlv]),
          },
          {
            name: '入库流量',
            data: data.map(el => [el.tm, el.inflow]),
          },
          {
            name: '供水流量',
            data: data.map(el => [el.tm, el.supplyFlow]),
          },
          {
            name: '泄洪流量',
            data: data.map(el => [el.tm, el.floodFlow]),
          },
        ]
      },
      
      save() {
        this.$emit('saveData', this.resultData.caseCode)
      },
    },
  }
</script>

<style lang="less" scoped>
.left {
  .item {
    display: flex;
    height: 36px;
    line-height: 24px;
    align-items: center;
    
    .label {
      // width: 120px;
      color: #4e5969;
      // text-align: right;
      font-size: 13px;
    }
    .value {
      flex: 1;
      color: #1d2129;
      font-weight: 500;
      padding-left: 8px;
      // 文字溢出隐藏
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
