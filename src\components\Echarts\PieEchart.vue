<template>
  <base-echart id="pie-echart" class="pie-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  import BaseEchart from './BaseEchart.vue'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      custom: { default: () => {} },
      width: { default: '100%' },
      height: { default: '300px' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource, this.custom)
      },
    },
    mounted() {},
    methods: {
      getOptions(data, custom) {
        let {
          shortValue = true, // 缩写坐标值
          xLabel = '', // x轴名称
          yLabel = '', //y轴名称
          yUnit = '', //y轴单位
          legend = false, // 图例
          showAreaStyle = true, // 颜色区域
          rYUnit = '', // 右侧y轴单位
          rYLabel = '', // 右侧y轴名称
          dataZoom = true,
          color = null,
        } = custom
        const levels = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        const option = {
          tooltip: {
            trigger: 'item',
          },
          legend: {
            show: false,
            // top: '5%',
            left: 'center',
            // doesn't perfectly work with our tricks, disable it
            selectedMode: false,
          },
          series: [
            {
              name: '水质占比数量',
              type: 'pie',
              radius: ['0', '50%'],
              // radius: ['40%', '50%'],
              center: ['50%', '50%'],
              // adjust the start angle
              startAngle: 180,
              label: {
                show: true,
                formatter(param) {
                  // correct the percentage
                  return param.name + ' (' + param.percent + '%)'
                },
              },
              data: data
                ? data
                : [
                    {
                      value: 0,
                      name: 'I类',
                      itemStyle: {
                        color: '#165dff',
                      },
                    },
                    {
                      value: 0,
                      name: 'II类',
                      itemStyle: {
                        color: '#165dff',
                      },
                    },
                    {
                      value: 0,
                      name: 'III类',
                      itemStyle: {
                        color: '#00b42a',
                      },
                    },
                    {
                      value: 0,
                      name: 'IV类',
                      itemStyle: {
                        color: '#f7ba1e',
                      },
                    },
                    {
                      value: 0,
                      name: 'V类',
                      itemStyle: {
                        color: '#ff7d00',
                      },
                    },
                    {
                      value: 0,
                      name: '劣V类',
                      itemStyle: {
                        color: '#f53f3f',
                      },
                    },
                  ],
            },
          ],
        }
        return option
      },
    },
  }
</script>
<style scoped></style>
