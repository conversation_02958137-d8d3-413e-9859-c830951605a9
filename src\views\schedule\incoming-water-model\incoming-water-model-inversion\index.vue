<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 上部分：标题和返回按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <h2 style="margin: 0; font-size: 20px; color: #1d2129; font-weight: 600;">来水反演</h2>
      <a-button @click="goBack" style="color: #4E5969; background: #fff; border-color: #E5E6EB; font-size: 14px; font-weight: 400;">
        返回
      </a-button>
    </div>

    <!-- 下部分 -->
    <div style="flex: 1; display: flex; flex-direction: column;">
      <!-- 时间选择器和按钮区域 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="color: #4E5969; font-size: 14px;">时间范围:</span>
          <a-range-picker 
            v-model="timeRange" 
            show-time 
            format="YYYY-MM-DD HH:mm"
            style="width: 350px;"
            placeholder="['开始时间', '结束时间']"
          />
        </div>
        <div>
          <a-button @click="handleReset" style="margin-right: 16px; color: #4E5969; background: #fff; border-color: #E5E6EB; font-size: 14px; font-weight: 400;">
            重置
          </a-button>
          <a-button type="primary" @click="handleQuery" style="color: #fff; background: #165DFF; font-size: 14px; font-weight: 400;">
            查询
          </a-button>
        </div>
      </div>

      <!-- 图表和表格展示区域 -->
      <div class="flood-box">
        <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 16px;">
          <!-- <p class="flood-tabs">
            <label class="name">来水反演</label>
          </p> -->
          <a @click="toggleTable" style="color: #165DFF; cursor: pointer; font-size: 14px;">
            {{ isTableExpanded ? '表格收起' : '表格展开' }}
          </a>
        </div>
        
        <div class="flood-content" :style="{ display: 'flex', flexDirection: 'row', width: '100%' }">
          <div :style="{ width: isTableExpanded ? '50%' : '100%' }">
            <ResultChart v-if="!!dataSource" :dataSource="dataSource" :showForecastBaseline="false" :key="chartKey" />
          </div>
          <div v-if="isTableExpanded" style="width: 50%; margin-left: 16px;">
            <ResultTable :dataSource="dataSource?.fcsts || []" :isShowTableHeader="false" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { queryAutoForecast } from '../services'
  import ResultChart from '../components/Chart.vue'
  import ResultTable from '../components/ResultTable.vue'
  import moment from 'moment'

  export default {
    name: 'IncomingWaterInversion',
    components: { ResultChart, ResultTable },

    data() {
      return {
        timeRange: [
          moment().subtract(3, 'days'),
          moment()
        ],
        dataSource: null,
        isTableExpanded: true,
        chartKey: 0,
      }
    },
    
    created() {
      this.getList();
    },
    
    methods: {
      getList() {
        const startTime = this.timeRange[0] ? this.timeRange[0].format('YYYY-MM-DD HH:mm') : '';
        const endTime = this.timeRange[1] ? this.timeRange[1].format('YYYY-MM-DD HH:mm') : '';
        
        // 使用相同的接口获取数据
        queryAutoForecast({
          fcstRange: '1',
          startTime: startTime,
          endTime: endTime,
        }).then(res => {
          this.dataSource = res.data;
        });
      },
      
      handleQuery() {
        if (!this.timeRange || !this.timeRange[0] || !this.timeRange[1]) {
          this.$message.warning('请选择时间范围');
          return;
        }
        this.getList();
      },
      
      handleReset() {
        this.timeRange = [
          moment().subtract(3, 'days'),
          moment()
        ];
        this.getList();
      },
      
      toggleTable() {
        this.isTableExpanded = !this.isTableExpanded;
        this.$nextTick(() => {
          this.chartKey += 1;
        });
      },
      
      goBack() {
        this.$router.go(-1);
      }
    },
  }
</script>

<style lang="less" scoped>
  .flood-box {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .flood-tabs {
      margin: 0;
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
    }
    
    .flood-content {
      flex: 1;
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
