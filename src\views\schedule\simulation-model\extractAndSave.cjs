const fs = require('fs');
const path = require('path');

// 读取 data.json 文件
const dataFilePath = path.join(__dirname, 'data.json');
const rawData = fs.readFileSync(dataFilePath, 'utf8');
const data = JSON.parse(rawData);

let temp = {
    "type": "FeatureCollection",
    "features": [],
    "totalFeatures": 43,
    "numberMatched": 43,
    "numberReturned": 43,
    "timeStamp": "2025-06-25T02:56:18.230Z",
    "crs": {
        "type": "name",
        "properties": {
            "name": "urn:ogc:def:crs:EPSG::4326"
        }
    }
}



// 定义 extractData 函数
function extractData(data1) {
    let result = [];
    for (const hedaoId in data1) {
        const { coord, data } = data1[hedaoId];
        coord.forEach((coords, index) => {
            let ts = []
            let temp1 = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": coords
                },
                "geometry_name": "geom",
                "properties": {
                    "idArr": index,
                }
            }
            for (const time in data) {
                if (!data[time]) continue
                const { wlevel, q } = data[time];
                ts.push([time, wlevel[index], q[index]])
            }
            temp1.properties.ts = ts
            result.push(temp1)
        });

    }
    console.log("result=============", result)
    return result;
}

// 提取数据
const extractedData = extractData(data);
temp.features = extractedData
// 将提取的数据保存到 data1.json 文件
const outputFilePath = path.join(__dirname, 'data1.json');
console.log("=============")
fs.writeFileSync(outputFilePath, JSON.parse(JSON.stringify(temp, null, 2)), 'utf8');


console.log('数据已成功提取并保存到 data1.json 文件。');