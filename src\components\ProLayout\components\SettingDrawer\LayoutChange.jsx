import PropTypes from 'ant-design-vue/es/_util/vue-types'

import 'ant-design-vue/es/tooltip/style'
import Tooltip from 'ant-design-vue/es/tooltip'
import 'ant-design-vue/es/list/style'
import List from 'ant-design-vue/es/list'
import 'ant-design-vue/es/select/style'
import Select from 'ant-design-vue/es/select'
import 'ant-design-vue/es/switch/style'
import Switch from 'ant-design-vue/es/switch'

export const renderLayoutSettingItem = (h, item) => {
  const action = { ...item.action }
  return (
    <Tooltip title={item.disabled ? item.disabledReason : ''} placement='left'>
      <List.Item actions={[action]}>
        <span style={{ opacity: item.disabled ? 0.5 : 1 }}>{item.title}</span>
      </List.Item>
    </Tooltip>
  )
}

export const LayoutSettingProps = {
  contentWidth: PropTypes.oneOf(['Fluid', 'Fixed']).def('Fluid'),
  fixedHeader: PropTypes.bool,
  fixSiderbar: PropTypes.bool,
  layout: PropTypes.oneOf(['sidemenu', 'topmenu'])
}

export default {
  props: LayoutSettingProps,
  render(h) {
    const { contentWidth, fixedHeader, layout, fixSiderbar } = this

    const handleChange = (type, value) => {
      this.$emit('change', { type, value })
    }

    return (
      <List
        split={false}
        dataSource={[
          {
            title: '内容区域宽度',
            action: (
              <Select
                value={contentWidth}
                size='small'
                onSelect={value => handleChange('contentWidth', value)}
                style={{ width: '80px' }}
              >
                {layout === 'sidemenu' ? null : <Select.Option value='Fixed'>{'Fixed'}</Select.Option>}
                <Select.Option value='Fluid'>{'Fluid'}</Select.Option>
              </Select>
            )
          },
          {
            title: '固定 Header',
            action: (
              <Switch size='small' checked={!!fixedHeader} onChange={checked => handleChange('fixedHeader', checked)} />
            )
          },
          {
            title: '固定侧边栏',
            disabled: layout === 'topmenu',
            // disabledReason: i18n('app.setting.fixedsidebar.hint'),
            action: (
              <Switch
                size='small'
                disabled={layout === 'topmenu'}
                checked={!!fixSiderbar}
                onChange={checked => handleChange('fixSiderbar', checked)}
              />
            )
          }
        ]}
        renderItem={(item, index) => renderLayoutSettingItem(h, item)}
      />
    )
  }
}
