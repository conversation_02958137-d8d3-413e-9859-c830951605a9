import './index.less'

import 'ant-design-vue/es/drawer/style'
import Drawer from 'ant-design-vue/es/drawer'
import SiderMenu, { SiderMenuProps } from './SiderMenu'

const SiderMenuWrapper = {
  name: 'SiderMenuWrapper',
  model: {
    prop: 'collapsed',
    event: 'collapse'
  },
  props: SiderMenuProps,
  render(h) {
    const { layout, collapsed } = this
    const isTopMenu = layout === 'topmenu'
    const handleCollapse = e => {
      this.$emit('collapse', true)
    }
    return !isTopMenu && <SiderMenu class='ant-pro-sider-menu' {...{ props: this.$props }} />
  }
}

SiderMenuWrapper.install = function (Vue) {
  Vue.component(SiderMenuWrapper.name, SiderMenuWrapper)
}

export { SiderMenu, SiderMenuProps }

export default SiderMenuWrapper
