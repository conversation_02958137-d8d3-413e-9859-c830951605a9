/**
 * 将颜色值HEX格式转换为rgb的格式
 * @param {hex} hex 需要转换的rgb字符串
 * @return {string}  ;
 */
export function hexToRgb(hex) {
  let str = hex.replace('#', '')
  if (str.length % 3) {
    return 'hex格式不正确！'
  }
  //获取截取的字符长度
  let count = str.length / 3
  //根据字符串的长度判断是否需要 进行幂次方
  let power = 6 / str.length
  let r = parseInt('0x' + str.substring(0 * count, 1 * count)) ** power
  let g = parseInt('0x' + str.substring(1 * count, 2 * count)) ** power
  let b = parseInt('0x' + str.substring(2 * count)) ** power

  // return `rgb(${r}, ${g}, ${b})`
  return [r, g, b]
}
