<!-- <template>
  <ant-modal
    :visible="open"
    modal-title="调整位置"
    
    modalWidth="860"
    modalHeight="680"
    @cancel="cancel"
    :footer="null"
  >
    <div slot="content" layout="vertical" id="olPoiMap" ref="olPoiMapRef">
      <div class="address-box" v-if="location">
        <img src="~@/assets/images/poi-marker-default.png" alt="" />
        &nbsp;
        <div class="address">{{ location }}</div>
      </div>

      <div class="fotter">
        <a-button @click="cancel" style="margin-right: 10px">取消</a-button>
        <a-button type="primary" @click="confirm">确认</a-button>
      </div>
    </div>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import { Map, View } from 'ol' //地图,视图
  import { OSM, XYZ } from 'ol/source' //可以理解为数据源,就是一张图片
  import TileLayer from 'ol/layer/Tile' //可以理解为图层
  import { Style, Stroke, Fill, Icon, Text, Circle } from 'ol/style'
  import { ZoomToExtent, ScaleLine, defaults as defaultControls } from 'ol/control'
  import { fromLonLat, transform } from 'ol/proj' //将坐标从经度/纬度转换为不同的投影。
  import OlFeature from 'ol/Feature'
  import OlGeomPoint from 'ol/geom/Point'
  import VectorSource from 'ol/source/Vector'
  import VectorLayer from 'ol/layer/Vector'

  export default {
    name: 'MapModal',
    props: {},
    components: { AntModal },
    data() {
      return {
        // 表单参数
        open: false,
        map: null,
        location: '',
        longitude: undefined,
        latitude: undefined
      }
    },
    created() {},
    mounted() {
      setTimeout(() => {
        this.$nextTick(() => {
          this.createMap(this.$refs['olPoiMapRef'])
        })
      }, 20)
    },
    computed: {},
    watch: {},
    methods: {
      createMap(mapNode) {
        const tileLayer = new TileLayer({
          // source: new XYZ({
          //   url: 'https://wprd01.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=7'
          // })
          source: new OSM({
            url: 'https://{a-c}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png'
          }),
          opacity: 0.5 // 地图透明度，取值范围为0-1
          // style: new Style({
          //   // stroke: new Stroke({
          //   //   color: 'yellow',
          //   //   width: 2
          //   // }),
          //   // fill: new Fill({
          //   //   color: 'rgba(0, 0, 0, 0.1)'
          //   // })
          // })
        })

        this.map = new Map({
          target: mapNode, // DOM容器
          layers: [tileLayer],
          view: new View({
            // OpenLayers默认使用EPSG:3857，fromLonLat将EPSG:4326的坐标转化为EPSG:3857的坐标
            center: fromLonLat([120.209947, 30.245853]),
            zoom: 11.4,
            maxZoom: 18,
            minZoom: 8
          }),
          controls: defaultControls({
            //加载控件到地图容器中
            zoom: true,
            rotate: false,
            attribution: false
          })
        })

        // 回显
        if (this.longitude && this.latitude) {
          this.addressAnalysis({ type: 'getAddress', lnglat: [this.longitude, this.latitude] }, () => {
            this.addMarker(transform([this.longitude, this.latitude], 'EPSG:4326', 'EPSG:3857'))
          })
        } else {
          if (this.location) {
            this.addressAnalysis({ type: 'getLngLat', address: this.location }, () => {
              this.addMarker(transform([this.longitude, this.latitude], 'EPSG:4326', 'EPSG:3857'))
            })
          }
        }

        // * 监听地图的单击事件，如果点击的是矢量元素则进行相关操作 */
        this.map.on('click', evt => {
          // 删除点
          this.map.removeLayer(this.map.getLayers().array_[1])

          // 增加点 marker
          this.addMarker(evt.coordinate)

          const lnglat = transform(evt.coordinate, 'EPSG:3857', 'EPSG:4326')
          this.longitude = lnglat[0]
          this.latitude = lnglat[1]

          this.addressAnalysis({ type: 'getAddress', lnglat })

          // 将坐标从'EPSG:3857'转换到'EPSG:4326'
          // transform(center, 'EPSG:3857', 'EPSG:4326')

          // 将坐标从'EPSG:4326'转换到'EPSG:3857'
          // transform(center, 'EPSG:4326', 'EPSG:3857')
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 确认
      confirm() {
        this.open = false
        this.$emit('confirm', { longitude: this.longitude, latitude: this.latitude, location: this.location })
      },
      /** 打开 */
      handleOpen(mapInfo) {
        this.open = true
        this.longitude = mapInfo.longitude
        this.latitude = mapInfo.latitude
        this.location = mapInfo.location
      },

      // 增加点 marker
      addMarker(coordinate) {
        const img = require('@/assets/images/poi-marker-default.png')
        // 创建marker
        const iconFeature = new OlFeature({
          geometry: new OlGeomPoint(coordinate)
          // data: data
        })
        // 设置style
        const iconStyle = new Style({
          image: new Icon({
            src: img,
            width: 26,
            height: 34,
            anchorXUnits: 'pixels',
            anchorYUnits: 'pixels',
            anchor: [24, 58]
          })
          // 设置marker的label
          // text: new Text({
          //   textAlign: 'center',
          //   textBaseline: 'top',
          //   // font: font,
          //   offsetX: 0,
          //   offsetY: 20,
          //   backgroundFill: new Fill({
          //     color: '#67C23A'
          //   }),
          //   padding: [0, 2, 0, 2],
          //   text: resolution < 1040 ? feature.get('index') : '',
          //   fill: new Fill({
          //     color: 'white'
          //   })
          // })
        })
        iconFeature.setStyle(iconStyle)
        // 图层添加marker
        let vectorSource = new VectorSource({
          features: [iconFeature]
        })
        var vectorLayer = new VectorLayer({
          source: vectorSource
        })
        this.map.addLayer(vectorLayer)
      },

      // (逆)地址解析
      addressAnalysis(info, callback) {
        window.AMap.plugin(['AMap.Geocoder'], () => {
          //加载地理编码插件
          let geocoder = new AMap.Geocoder()
          if (info.type === 'getLngLat') {
            //返回地理编码结果
            geocoder.getLocation(info.address, (status, result) => {
              if (status === 'complete' && result.geocodes.length) {
                const location = result.geocodes[0].location
                this.longitude = location.lng
                this.latitude = location.lat
                callback && callback()
              } else {
                console.error('根据地址查询位置失败')
              }
            })
          }
          if (info.type === 'getAddress') {
            //逆地理编码
            geocoder.getAddress(info.lnglat, (status, result) => {
              if (status === 'complete' && result.regeocode) {
                let address = result.regeocode.formattedAddress
                this.location = address
                callback && callback()
              } else {
                console.error('根据经纬度查询地址失败')
              }
            })
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    .modal-content {
      width: 100%;
      height: 100%;
      #olPoiMap {
        width: 100%;
        height: 100%;
        position: relative;
        .address-box {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%);
          background-color: #fff;
          padding: 5px 10px;
          z-index: 1;
          display: flex;
          align-items: center;
          > img {
            width: 16px;
            height: 20px;
          }
          > .address {
            flex: 1;
          }
        }
        .fotter {
          position: absolute;
          right: 0;
          bottom: 0;
          margin: 0 10px 10px 0;
          z-index: 1;
        }
      }
    }
  }
</style> -->
