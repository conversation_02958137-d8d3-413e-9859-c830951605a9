<template>
  <div>
    <a-transfer
      :titles="['选择项', '已选项']"
      :data-source="dataSource"
      :target-keys="targetKeys"
      :disabled="disabled"
      :show-select-all="false"
      @change="onChange"
    >
      <template
        slot="children"
        slot-scope="{
          props: { direction, filteredItems, selectedKeys, disabled: listDisabled },
          on: { itemSelectAll, itemSelect },
        }"
      >
        <div class="table-box">
          <a-table
            :row-selection="
              getRowSelection({
                disabled: listDisabled,
                selectedKeys,
                itemSelectAll,
                itemSelect,
              })
            "
            :columns="direction === 'left' ? leftColumns : rightColumns"
            :data-source="filteredItems"
            :pagination="false"
            size="small"
            :style="{ pointerEvents: listDisabled ? 'none' : null }"
            :custom-row="
              ({ key, disabled: itemDisabled }) => ({
                on: {
                  click: () => {
                    if (itemDisabled || listDisabled) return
                    itemSelect(key, !selectedKeys.includes(key))
                  },
                },
              })
            "
          />
        </div>
      </template>
    </a-transfer>
  </div>
</template>
<script lang="jsx">
  import AdvanceTable from '@/components/pt/table/AdvanceTable'

  import 'ant-design-vue/es/transfer/style'
  import * as _ from 'lodash'
  import ATransfer from 'ant-design-vue/es/transfer'
  import difference from 'lodash/difference'

  function isChecked(selectedKeys, eventKey) {
    return selectedKeys.indexOf(eventKey) !== -1
  }

  function handleTreeData(data, targetKeys = []) {
    data.forEach(item => {
      item['disabled'] = targetKeys.includes(item.id)
      if (item.children) {
        handleTreeData(item.children, targetKeys)
      }
    })
    return data
  }

  export default {
    name: 'ProjectTransfer',
    components: {
      ATransfer,
      AdvanceTable,
    },
    props: [
      'dataOptions',
      'propsTargetKeys',
      'typeTargetKeys',
      'dataTargetKeys',
      'transferLeftColumns',
      'transferRightColumns',
    ],
    data() {
      let dataArr = []
      return {
        targetKeys: this.dataTargetKeys, //[],
        dataSource: this.dataOptions,
        disabled: false,
        dataArr: dataArr,

        leftColumns: this.transferLeftColumns,
        rightColumns: this.transferRightColumns,

        showSearch: true,
        checkedList: [],
        checked: [],
      }
    },
    computed: {},
    created() {},
    watch: {
      dataOptions: {
        immediate: true,
        handler: function (newVal, oldVal) {
          this.dataSource = newVal
        },
      },
      dataTargetKeys: {
        immediate: true,
        handler: function (newVal, oldVal) {
          this.targetKeys = newVal
        },
      },
      transferLeftColumns: {
        immediate: true,
        handler: function (newVal, oldVal) {
          this.leftColumns = newVal
        },
      },
      transferRightColumns: {
        immediate: true,
        handler: function (newVal, oldVal) {
          this.rightColumns = newVal
        },
      },
      checkedList(newVal, oldVal) {
        this.$emit('selected-change', newVal, oldVal)
      },
    },
    methods: {
      onChange(nextTargetKeys) {
        this.checkedList = []
        this.targetKeys = []
        this.targetKeys = nextTargetKeys
        for (let i = 0; i < this.targetKeys.length; i++) {
          const found = this.dataOptions.find(item => item.key == this.targetKeys[i])
          if (found) {
            this.checkedList.push(found)
          }
        }
        // this.checked = this.targetKeys?.map(el =>
        //   `${el.slice(1)}`
        // ) || []
      },

      /* 设备配置 */
      getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
        return {
          getCheckboxProps: item => ({
            props: { disabled: disabled || item.disabled },
          }),
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ key }) => key)
            const diffKeys = selected
              ? difference(treeSelectedKeys, selectedKeys)
              : difference(selectedKeys, treeSelectedKeys)
            itemSelectAll(diffKeys, selected)
          },
          onSelect({ key }, selected) {
            itemSelect(key, selected)
          },
          selectedRowKeys: selectedKeys,
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep.tree-transfer {
    // border: 1px solid #fff;
    height: 750px;
  }
  ::v-deep .ant-transfer-list-body {
    overflow: auto;
  }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #2f54eb;
  }
  ::v-deep .tree-transfer .ant-transfer-list:first-child {
    width: 50%;
    flex: none;
  }

  ::v-deep .ant-tree-treenode-switcher-open {
    // align-items: center;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  ::v-deep .ant-tree-child-tree li {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  ::v-deep .ant-tree-treenode-switcher-close {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  ::v-deep .ant-tree li span.ant-tree-switcher {
    line-height: normal;
  }

  .table-box {
    position: absolute;
    height: 100%;
    background-color: #f0f0f0;

    overflow: hidden;
    position: relative;
    ::v-deep .ant-table-thead {
      position: sticky;
      top: 0px;
      z-index: 2;
    }
  }
</style>
