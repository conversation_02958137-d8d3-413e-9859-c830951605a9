<template>
  <div>
    <ant-modal :visible="open" :modal-title="modalTitle" modalWidth="800" @cancel="cancel" modalHeight="500">
      <div class="param-set-content" slot="content">
        <advance-table
          :columns="columns"
          bordered
          :data-source="list"
          :title="tableTitle"
          :loading="loading"
          rowKey="rowKey"
          size="middle"
          tableKey="param-set-table"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :format-conditions="true"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button> -->
            <a-button type="danger" v-if="!multiple" @click="handleDelete" style="margin-left: 10px">
              <a-icon type="delete" />
              清除终端
            </a-button>
          </div>
        </advance-table>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" :loading="btnLoading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'

  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getOptions, getObjectIndex, addObjectIndex, deleteObjectIndex } from '@/api/common'
  import { siteTerminalAll } from '@/views/system/monitor-data/site-maintain/services'

  export default {
    name: 'ParamSet',
    props: {},
    components: {
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        open: false,
        btnLoading: false,
        modalTitle: '',
        rowInfo: {},
        isShowTableModal: false,
        dragging: false,

        reqType: 'add',
        form: {
          objectCategoryId: undefined,
          objectId: undefined,
          objectType: undefined,
        },

        monitoringIndexOptions: [],
        siteTerminalAll: [],

        searchTValue: '',
        tableTitle: ' ',
        loading: false,
        selectedRowKeys: [],
        selectedRows: [],
        ids: [],
        single: true,
        multiple: true,
        list: [],
        columns: [
          {
            title: '序号',
            width: 55,
            customRender: (_, r, i) => {
              return i + 1
            },
          },
          {
            title: '监测指标',
            dataIndex: 'indexCode',
            width: 180,
            minWidth: 180,
            customRender: (_, r, i) => {
              const cIndexs = this.list.map(el => el.indexCode)

              const cOptions = this.monitoringIndexOptions
              return (
                <div class='indicator-cell'>
                  <a-select style={{ width: '100%' }} v-model={this.list[i].indexCode} disabled>
                    {cOptions?.map((el, j) => {
                      return (
                        <a-select-option key={j} value={el.key}>
                          {el.value}
                        </a-select-option>
                      )
                    })}
                  </a-select>
                </div>
              )
            },
          },
          {
            title: '展示名称',
            minWidth: 100,
            showOverflow: 'tooltip',
            customRender: (v, r, i) => {
              return (
                // <span style='text-align: center;width:100%;'>{this.list[i].displayName}</span>

                <a-input value={this.list[i].displayName} style='text-align: center;' disabled></a-input>
              )
            },
          },
          {
            title: '终端编码',
            colSpan: 1,
            width: 130,
            minWidth: 130,
            customRender: (v, r, i) => {
              const tOptions = this.siteTerminalAll.find(el => el.indexCode === r.indexCode)?.indexCodes || []

              const terminalOptions = JSON.parse(JSON.stringify(tOptions)).filter(
                el => el.terminalName.indexOf(this.searchTValue) >= 0,
              )

              const obj = {
                attrs: { colSpan: 2 },
                children: (
                  <div>
                    <div class='terminal-content'>
                      <draggable
                        list={this.list[i].indexCodes}
                        animation='300'
                        disabled={false}
                        class='list-group'
                        ghost-class='ghost'
                        onStart={() => (this.dragging = true)}
                        onEnd={() => (this.dragging = false)}
                      >
                        <div class='terminal-box'>
                          <a-select
                            style={{ width: '100%' }}
                            v-model={this.list[i].terminalId}
                            placeholder='请选择'
                            showSearch={true}
                            filter-option={false}
                            onSearch={this.fetchTerminal}
                          >
                            {terminalOptions?.map((el, k) => {
                              return (
                                <a-select-option key={k} value={el.terminalId}>
                                  <div class='terminal-select-item'>
                                    <span>{el.terminalCode}</span>
                                    <span>{el.terminalName}</span>
                                  </div>
                                </a-select-option>
                              )
                            })}
                          </a-select>
                        </div>
                      </draggable>
                    </div>
                  </div>
                ),
              }

              return obj
            },
          },
          {
            title: '终端名称',
            width: 130,
            minWidth: 130,
            customRender: (v, r, i) => {
              const obj = {
                attrs: { colSpan: 0 },
              }
              return obj
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('monitoringIndex').then(res => {
        this.monitoringIndexOptions = res.data
      })
    },
    mounted() {},
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        // this.$emit('close')
      },
      handleShow(record) {
        this.open = true

        this.modalTitle = record.objectCategoryName + '监测指标'
        this.form = {
          objectCategoryId: record.objectCategoryId,
          objectId: record?.objectId,
          objectType: record?.objectType,
        }

        this.rowInfo = record

        this.loading = true
        siteTerminalAll().then(resp => {
          this.siteTerminalAll = resp?.data || []
          let getParams = {
            objectCategoryId: record?.objectCategoryId,
            objectId: record?.objectId,
            objectType: record?.objectType,
          }

          getObjectIndex(getParams).then(res => {
            this.loading = false
            if (res?.data?.indexs?.length) {
              this.reqType = 'edit'
            } else {
              this.reqType = 'add'
            }
            {
              /* this.form.objectCategoryId = record.objectCategoryId */
            }

            this.list = (res?.data || []).map((el, i) => ({ ...el, rowKey: i }))
          })
        })
      },

      //搜索
      fetchTerminal(val) {
        this.searchTValue = val
      },

      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        {
          /* this.ids = this.selectedRows.map(item => item.rowKey) */
        }
        this.ids = this.selectedRows.map(item => item.id)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },

      onIndexCodeChange(v, i) {
        // this.list[i].indexCodes = [
        //   {
        //     terminalId: undefined,
        //     indexCode: undefined,
        //     terminalCode: undefined,
        //     terminalName: undefined,
        //     sort: null,
        //   },
        // ]
      },
      onDisplayNameChange(v, i) {
        this.list?.forEach((el, index) => {
          if (el.rowKey == i.rowKey) {
            el.displayName = v
          }
        })
      },

      // 增加指标
      handleAdd() {
        this.list.push({
          rowKey: this.list.length,
          indexCode: null,
          displayName: null,
          objectCategoryId: this.form.objectCategoryId,
          objectId: this.form.objectId,
          objectType: this.form.objectType,
          terminalId: undefined,
          objectCategoryIndexId: undefined,
        })
      },
      // 删除指标
      handleDelete() {
        deleteObjectIndex({ ids: this.ids?.join(',') }).then(res => {
          // this.$message.success('删除成功', 3)
        })
        this.list.forEach(item => {
          console.log(' list del ', item)
          if (this.ids.includes(item.id)) {
            item.terminalId = null
          }
        })
        this.onSelectChange([], [])
      },

      // checkMove: function (e) {
      //   window.console.log('Future index: ' + e.draggedContext.futureIndex)
      // },

      /** 提交按钮 */
      confirm() {
        this.btnLoading = true

        // if (!this.list.every(item => item.terminalId !== null)) {
        //   this.$message.info('终端不能为空，请选择！', 3)
        //   this.btnLoading = false
        //   return
        // }
        this.list = this.list?.filter(item => item.terminalId !== null && item.terminalId !== '')
        console.log(' list save ', this.list)
        const params = this.list?.map(el => {
          return {
            ...el,
            objectType: this.form.objectType,
            objectId: this.form.objectId,
          }
        })

        if (params.length > 0) {
          addObjectIndex(params)
            .then(res => {
              this.$message.success('配置成功', 3)
              this.open = false
              this.$emit('ok')
            })
            .catch(err => (this.btnLoading = false))
        } else {
          this.open = false
          this.$emit('ok')
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    // overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  .add-terminal {
    .add-terminal-btn {
      padding: 0 8px;
      height: 24px;
      min-width: 50px;
    }
  }

  .indicator-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .indicator-btn {
    //   width: 60px;
    // }
  }

  .terminal-select-item {
    display: flex;
    width: 100%;
    justify-content: space-between;
    > span {
      flex: 1;
      text-align: center;
    }
  }
  .terminal-content {
    .terminal-box {
      background-color: #f1f1f1;
      cursor: move;
      border-radius: 12px;
      padding: 6px 10px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      ::v-deep .ant-select-selection-selected-value {
        width: 100%;
      }

      .terminal-item-operation {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        .terminal-delete {
          margin-left: 20px;
          cursor: pointer;
          color: @primary-color;
        }
      }
    }
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
  .list-group {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    .list-group-item {
      cursor: move;
      position: relative;
      display: block;
      margin-bottom: -1px;
      background-color: #fff;
      border: 1px solid rgba(0, 0, 0, 0.125);
    }
  }
</style>
