<template>
  <div class="easy-player-wrapper">
    <EasyPlayer
      class="easy-player"
      @error="restartPlayer"
      @ended="restartPlayer"
      @play="videoPlay"
      ref="EasyPlayerRef"
      :videoUrl="videoSrc"
      :aspect="aspect"
      :showEnterprise="false"
      :show-custom-button="false"
      :alt="alt"
      :autoplay="autoplay"
      :loop="loop"
      :muted="muted"
      :easyStretch="false"
      fluent
      stretch
    ></EasyPlayer>
    <div class="steering-wheel" v-if="videoSrc && isShowDirection">
      <div class="btn-box">
        <a-icon type="up" class="btn-up btn-common" @click="onDirection('up')" />
        <a-icon type="down" class="btn-down btn-common" @click="onDirection('down')" />
        <a-icon type="left" class="btn-left btn-common" @click="onDirection('left')" />
        <a-icon type="right" class="btn-right btn-common" @click="onDirection('right')" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import EasyPlayer from '@easydarwin/easyplayer'

  export default {
    name: 'VideoEasyPlayer',
    components: {
      EasyPlayer,
    },
    props: {
      videoSrc: {
        type: String,
        default: '',
      },
      isShowDirection: {
        type: Boolean,
        default: true,
      },
      aspect: {
        type: String,
        default: '16:9',
      },
      alt: {
        type: String,
        default: '无信号',
      },
      autoplay: {
        type: Boolean,
        default: true,
      },
      loop: {
        type: Boolean,
        default: true,
      },
      muted: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        timer: '',
      }
    },
    methods: {
      onDirection(direction) {
        this.$emit('setDirection', direction)
      },
      restartPlayer() {
        this.$refs.EasyPlayerRef.initPlayer()
        // this.$emit("pullFlow")
        // this.timer = setInterval(() => {
        //   this.$refs.EasyPlayerRef.initPlayer();
        //   this.$emit("pullFlow")
        // }, 5000)
      },
      videoPlay(a) {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
      },
    },
    destroyed() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
  }
</script>
<style lang="less" scoped>
  .easy-player-wrapper {
    width: 100%;
    height: 100%;
  }
  .easy-player {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .steering-wheel {
    width: 244px;
    height: 244px;
    position: absolute;
    z-index: 999;
    right: 20px;
    top: 50%;
    background: url('../../assets/images/steering-wheel.png') no-repeat;
    background-size: 100% 100%;
    font-size: 0.2rem;

    .btn-box {
      width: 100%;
      height: 100%;
      position: relative;
      .btn-common {
        position: absolute;
        width: 50px;
        height: 50px;
        color: #b6b6b7;
        font-size: 30px;
        :active {
          color: #4cdeff;
        }
      }
      .btn-up {
        left: 50%;
        top: 20px;
        transform: translate(-50%);
      }
      .btn-down {
        left: 50%;
        bottom: 0;
        transform: translate(-50%);
      }
      .btn-left {
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .btn-right {
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  ::v-deep .vjs-fluid:not(.vjs-audio-only-mode) {
    padding-top: 0;
  }
</style>
