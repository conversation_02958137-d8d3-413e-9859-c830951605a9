import Vue from 'vue'
/**
 * @description 超级用户常量
 */
const SUPERUSER = 'superadmin'
/**
 * @description 默认应用类型
 */

console.log('appid', process.env.VUE_APP_APPID)
const APPID = process.env.VUE_APP_APPID || 'base'
/**
 * @description 默认密码
 */
const INITPWD = '123456'

/**
 * @description 性别
 */
const SEX = [
  { dictKey: '1', dictValue: '男' },
  { dictKey: '2', dictValue: '女' },
]

/**
 * @description 中文序号
 */
const ChineseNumerals = [
  '一',
  '二',
  '三',
  '四',
  '五',
  '六',
  '七',
  '八',
  '九',
  '十',
  '十一',
  '十二',
  '十三',
  '十四',
  '十五',
  '十六',
  '十七',
  '十八',
  '十九',
  '二十',
  '二十一',
  '二十二',
  '二十三',
  '二十四',
  '二十五',
  '二十六',
  '二十七',
  '二十八',
  '二十九',
  '三十',
  '三十一',
  '三十二',
  '三十三',
  '三十四',
  '三十五',
  '三十六',
  '三十七',
  '三十八',
  '三十九',
  '四十',
  '四十一',
  '四十二',
  '四十三',
  '四十四',
  '四十五',
  '四十六',
  '四十七',
  '四十八',
  '四十九',
  '五十',
  '五十一',
  '五十二',
  '五十三',
  '五十四',
  '五十五',
  '五十六',
  '五十七',
  '五十八',
  '五十九',
  '六十',
  '六十一',
  '六十二',
  '六十三',
  '六十四',
  '六十五',
  '六十六',
  '六十七',
  '六十八',
  '六十九',
  '七十',
  '七十一',
  '七十二',
  '七十三',
  '七十四',
  '七十五',
  '七十六',
  '七十七',
  '七十八',
  '七十九',
  '八十',
  '八十一',
  '八十二',
  '八十三',
  '八十四',
  '八十五',
  '八十六',
  '八十七',
  '八十八',
  '八十九',
  '九十',
  '九十一',
  '九十二',
  '九十三',
  '九十四',
  '九十五',
  '九十六',
  '九十七',
  '九十八',
  '九十九',
  '一百',
  '一百零一',
  '一百零二',
  '一百零三',
  '一百零四',
  '一百零五',
  '一百零六',
  '一百零七',
  '一百零八',
  '一百零九',
  '一百一十',
  '一百一十一',
  '一百一十二',
  '一百一十三',
  '一百一十四',
  '一百一十五',
  '一百一十六',
  '一百一十七',
  '一百一十八',
  '一百一十九',
  '一百二十',
  '一百二十一',
  '一百二十二',
  '一百二十三',
  '一百二十四',
  '一百二十五',
  '一百二十六',
  '一百二十七',
  '一百二十八',
  '一百二十九',
  '一百三十',
  '一百三十一',
  '一百三十二',
  '一百三十三',
  '一百三十四',
  '一百三十五',
  '一百三十六',
]
//保存全局状态
let State = new Vue({
  data() {
    return {
      is_phone: false,
    }
  },
})
export default {
  SUPERUSER,
  APPID,
  INITPWD,
  SEX,
  State,
  ChineseNumerals,
}
