import PropTypes from 'ant-design-vue/es/_util/vue-types'

import 'ant-design-vue/es/tooltip/style'
import Tooltip from 'ant-design-vue/es/tooltip'
import 'ant-design-vue/es/icon/style'
import Icon from 'ant-design-vue/es/icon'

const BlockCheckboxProps = {
  value: PropTypes.string,
  // Item: { key, url, title }
  list: PropTypes.array
}

const baseClassName = 'ant-pro-setting-drawer-block-checbox'
const BlockCheckbox = {
  props: BlockCheckboxProps,
  render(h) {
    const { value, list } = this
    const items = list || [
      {
        key: 'sidemenu',
        url: 'https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg',
        title: '侧边菜单布局'
      },
      {
        key: 'topmenu',
        url: 'https://gw.alipayobjects.com/zos/antfincdn/URETY8%24STp/KDNDBbriJhLwuqMoxcAr.svg',
        title: '顶部菜单布局'
      }
    ]

    const handleChange = key => {
      this.$emit('change', key)
    }

    const disableStyle = {
      cursor: 'not-allowed'
    }

    return (
      <div class={baseClassName} key={value}>
        {items.map(item => (
          <Tooltip title={item.title} key={item.key}>
            <div
              class={`${baseClassName}-item`}
              style={item.disable && disableStyle}
              onClick={() => !item.disable && handleChange(item.key)}
            >
              <img src={item.url} alt={item.key} />
              <div
                class={`${baseClassName}-selectIcon`}
                style={{
                  display: value === item.key ? 'block' : 'none'
                }}
              >
                <Icon type='check' />
              </div>
            </div>
          </Tooltip>
        ))}
      </div>
    )
  }
}

export default BlockCheckbox
