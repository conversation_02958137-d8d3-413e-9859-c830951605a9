<template>
  <div>
    <a-transfer
      class="tree-transfer"
      :data-source="dataSource"
      :target-keys="targetKeys"
      :render="item => item.title"
      @change="onChange"
      showSearch
      @search="onSearchChange"
    >
      <template slot="children" slot-scope="{ props: { direction, selectedKeys }, on: { itemSelect } }">
        <a-tree
          v-if="direction === 'left'"
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          @expand="onExpand"
          defaultExpandAll
          blockNode
          :show-icon="true"
          checkable
          :selectable="false"
          :checkedKeys="getCheckedKeys(selectedKeys, targetKeys)"
          :treeData="treeData"
          :replaceFields="{ children: 'children', title: 'title', key: 'key' }"
          @check="
            (_, props) => {
              onChecked(_, props, [...selectedKeys, ...targetKeys], itemSelect)
            }
          "
        >
          <SvgIcon slot="org" iconClass="companyFill" class="depIcon" />
          <SvgIcon slot="company" iconClass="company" class="depIcon" />
          <SvgIcon slot="dept" iconClass="connections" class="depIcon" />
          <a-icon slot="user" type="user" class="depIcon" />

          <template slot="title" slot-scope="{ title }">
            <span v-if="title.indexOf(searchValue) > -1">
              {{ title.substr(0, title.indexOf(searchValue)) }}
              <span style="color: #f50">{{ searchValue }}</span>
              {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
            </span>
            <span v-else>{{ title }}</span>
          </template>
        </a-tree>
      </template>
    </a-transfer>
  </div>
</template>

<script lang="jsx">
  /**
   * 场景：多选，只选叶子节点
   */
  import 'ant-design-vue/es/transfer/style'
  import * as _ from 'lodash'
  import ATransfer from 'ant-design-vue/es/transfer'
  import { default as getFlatTreeMap, getFlatTree } from '@/utils/getMapFlatTree.js'

  function handleTreeData(data, targetKeys = []) {
    data.forEach(item => {
      item['disabled'] = targetKeys.includes(item.key)
      if (item.children) {
        handleTreeData(item.children, targetKeys)
      }
    })
    return data
  }

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children && node.children.length) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  export default {
    name: 'TreeTransfer',
    components: { ATransfer },
    props: ['options', 'targetKeys'],
    data() {
      let dataArr = []

      function transferData(arr, newArr) {
        arr.forEach((item, i) => {
          newArr.push({
            ...item,
            key: item.key + '',
            title: item.name,
            children: [],
            slots: { icon: item.type == 'data' ? 'user' : 'dept' }, // TODO,
            scopedSlots: { title: 'title' },
          })
          if (item.children?.length) {
            transferData(item.children, newArr[i].children)
          }
        })
      }
      transferData(JSON.parse(JSON.stringify(this.options)), dataArr)

      const dataSource = JSON.parse(JSON.stringify(getFlatTree(dataArr)))
      return {
        dataSource: dataSource,
        dataArr: dataArr,

        searchValue: undefined,
        expandedKeys: dataSource.map(el => el.key),
        autoExpandParent: true,
      }
    },
    computed: {
      treeData() {
        return handleTreeData(this.dataArr, this.targetKeys)
      },
    },

    created() {},
    methods: {
      onSearchChange(direction, value) {
        const expandedKeys = this.dataSource.map(item => {
          if (item.title.indexOf(value) > -1) {
            return getParentKey(item.key, this.dataArr)
          }
          return null
        })

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },

      onChange(targetKeys) {
        let arr = [...new Set(targetKeys)]
        let allLeaf = []
        function getAllChild(array) {
          array.forEach(item => {
            if (item.isLeaf) {
              allLeaf.push(item)
            } else {
              getAllChild(item.children)
            }
          })
        }
        targetKeys.forEach((ele, index) => {
          this.dataSource.forEach(el => {
            if (ele === el.key && !el.isLeaf) {
              allLeaf = []
              getAllChild(el.children || [])
              arr = [...arr, ...allLeaf.map(item => item.key)]
            }
          })
        })

        this.$emit(
          'update:targetKeys',
          _.difference(
            [...new Set(arr)],
            [...new Set(arr)].filter(el => this.dataSource.find(ele => el === ele.key && !ele.isLeaf)),
          ),
        )
      },
      onChecked(_, e, checkedKeys, itemSelect) {
        const { eventKey } = e.node

        // TODO
        itemSelect(eventKey, !checkedKeys.includes(eventKey))
      },

      getCheckedKeys(selectedKeys, targetKeys) {
        return [...selectedKeys, ...targetKeys]
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .tree-transfer .ant-transfer-list:first-child {
    width: 50%;
    flex: none;
  }

  ::v-deep .ant-transfer-list-body {
    max-height: 380px;
    overflow-y: auto;
  }

  ::v-deep .ant-transfer-customize-list .ant-transfer-list:last-child .ant-transfer-list-body-search-wrapper {
    display: none;
  }

  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
  }
  ::v-deep .ant-transfer-list-header {
    background: rgba(170, 196, 255, 0.25);
  }
  ::v-deep .ant-transfer-operation {
    margin: 0 15px;
    .ant-btn {
      border-radius: 15px;
    }
  }
</style>
