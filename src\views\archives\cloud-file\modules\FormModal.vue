<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="260"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model
          ref="form"
          :model="form"
          :rules="{ folderName: [{ required: true, message: '请输入名称', trigger: 'blur' }] }"
        >
          <a-form-model-item
            label="名称"
            prop="folderName"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <a-input v-model="form.folderName" allowClear />
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'

  import { addFolder } from '../services'

  export default {
    name: 'FormModal',
    props: {},
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,

        // 弹框名称
        formTitle: '',
        open: false,

        // 表单参数
        form: {
          parentId: undefined,
          folderName: undefined,
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
        this.form.parentId = parentId
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        this.form = record
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            addFolder(this.form)
              .then(response => {
                this.$message.success('新增成功', 3)
                this.open = false
                this.$emit('close')
                this.$emit('ok')
              })
              .finally(() => (this.loading = false))
          } else {
            return false
          }
        })
      },
    },
  }
</script>
