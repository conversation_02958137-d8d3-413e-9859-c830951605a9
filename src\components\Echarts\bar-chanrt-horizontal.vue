<template>
  <base-echart id="bar-echart" class="bar-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'

  export default {
    components: {
      BaseEchart
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: []
          }
        ]
      },
      custom: { default: () => {} },
      width: { default: '100%' },
      height: { default: '300px' }
    },
    data() {
      return {}
    },
    mounted() {},
    computed: {
      options() {
        return this.getOptions(this.dataSource, this.custom)
      }
    },
    methods: {
      getOptions(data, custom) {
        let {
          shortValue = true, // 缩写坐标值
          xLabel = '', // x轴名称
          yLabel = '', //y轴名称
          yUnit = '', //y轴单位
          rYUnit = '', // 右侧y轴单位
          rYLabel = '', // 右侧y轴名称
          legend = false, // 图例
          singleValue = true,
          inverse,
          dataZoom = true
        } = custom

        let xAxisParams = {
          type: 'category',
          data: data.length ? data[0].data.map(i => i[0]) : [],
          axisLabel: {
            textStyle: {
              color: '#000'
            }
          }
        }
        let yAxisParams = {
          type: 'value'
        }

        let legendParams = {
          top: 5,
          left: '50%'
        }

        if (inverse) {
          let temp
          temp = xAxisParams
          xAxisParams = yAxisParams
          yAxisParams = temp

          temp = xLabel
          xLabel = yLabel
          yLabel = temp

          legendParams = {
            bottom: 0,
            left: 'center'
          }

          xAxisParams.splitLine = {
            show: false
          }
          xAxisParams.axisLabel = {
            textStyle: {
              color: 'rgba(246, 255, 255, 0.7)'
            },
            formatter(t) {
              if (shortValue) {
                if (t > Math.pow(10, 8) - 1) {
                  return t / Math.pow(10, 9) + '亿'
                }
                if (t > Math.pow(10, 5) - 1) {
                  return t / 10000 + '万'
                }
              }
              if (yUnit) {
                return `${t}${yUnit}`
              }
              return t
            }
          }
        }

        const option = {
          title: {
            top: 5,
            left: 5,
            // text: yLabel,
            textAlign: 'left',
            textStyle: {
              color: 'rgba(246, 255, 255, 0.7)',
              fontSize: 12,
              fontWeight: 400
            }
          },
          grid: {
            left: '4%',
            right: xLabel ? '10%' : '10%',
            bottom: inverse ? '12%' : '5%',
            top: inverse ? '12%' : '5%',
            containLabel: true
          },
          tooltip: {
            // confine: true,
            trigger: 'axis',
            // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
            borderWidth: 0,
            textStyle: {
              color: '#000'
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
            // formatter: params => {
            //   let dataStr = `<div><p style="font-weight:bold;margin:0 5px 5px;">${params[0].name}</p></div>`;
            //   params.forEach(item => {
            //     dataStr += `<div>
            //    <div style="margin: 0 8px;">
            //      <span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${
            //        item.color.colorStops[0].color
            //      };border-radius:2px;"></span>
            //      <span>${singleValue  true? item.name : item.seriesName}</span>
            //      <span style="float:right;color:#fff;margin-left:20px;">${
            //        item.data
            //      }</span>
            //    </div>
            //  </div>`;
            //   });
            //   return dataStr;
            // }
          },
          yAxis: {
            name: (data.length && xLabel) || '',
            nameTextStyle: {
              padding: [0, 0, 0, -5],
              color: '#000',
              fontSize: 12,
              fontWeight: 400
            },
            axisTick: {
              show: true
            },
            axisLine: {
              show: true
            },
            axisLabel: {
              textStyle: {
                color: '#000'
              }
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: '#000'
              }
            },
            z: 10,
            ...xAxisParams
          },
          xAxis: [
            {
              name: yLabel || '',
              axisPointer: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: 'dashed',
                  color: '#BBB'
                }
              },
              axisLabel: {
                textStyle: {
                  color: '#000000'
                },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 5) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${yUnit}`
                  }
                  return t
                }
              },
              ...yAxisParams
            },
            {
              name: rYLabel || '',
              position: 'right',
              axisPointer: {
                show: false
              },
              axisLine: {
                show: false
              },
              splitLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#000'
                },
                formatter(t) {
                  if (shortValue) {
                    if (t > Math.pow(10, 8) - 1) {
                      return t / Math.pow(10, 9) + '亿'
                    }
                    if (t > Math.pow(10, 4) - 1) {
                      return t / 10000 + '万'
                    }
                  }
                  if (yUnit) {
                    return `${t}${rYUnit}`
                  }
                  return t
                }
              }
            }
          ],

          legend: {
            show: !!custom?.legend,
            icon: 'roundRect',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: '#000'
            },
            ...legendParams,
            ...legend
          },
          series: data.map((item, index) => {
            return {
              type: 'bar',
              barMaxWidth: 26,
              showBackground: true,
              // backgroundStyle: {
              //   color: 'rgba(255, 255, 255, 0.05)'
              // },
              name: item.name,
              stack: item.stack,
              yAxisIndex: item?.yAxisIndex || 0,
              itemStyle: {
                borderRadius: [2, 2, 0, 0], //（顺时针左上，右上，右下，左下）
                color: 'rgba(64, 93, 249, 1)'
              },
              emphasis: {
                itemStyle: {
                  color: 'rgba(64, 93, 249, 0.6)'
                }
              },
              data: item.data.map(d => d[1])
            }
          })
        }
        return option
      }
    }
  }
</script>

<style scoped></style>
