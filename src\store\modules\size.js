import storage from 'store'
import debounce from 'lodash/debounce'

const size = {
  state: {
    scaleNum: 1,
    clientHeight: 0
  },

  mutations: {
    WINDOW_RESIZE: (state, clientHeight) => {
      state.clientHeight = clientHeight
      storage.set('clientHeight', clientHeight)
    }
  },

  actions: {
    onWindowResize: debounce(({ commit }) => {
      // 获取 html 元素
      let screenHtml = document.querySelector('html')
      if (!screenHtml) return

      // html 的fontsize 大小
      let htmlRem
      let scaleNum
      if (screenHtml.clientWidth / screenHtml.clientHeight > 1920 / 1080) {
        htmlRem = (screenHtml.clientHeight * 100) / 1080
        scaleNum = screenHtml.clientHeight / 1080
      } else {
        htmlRem = (screenHtml.clientWidth * 100) / 1920
        scaleNum = screenHtml.clientWidth / 1920
      }

      screenHtml.style.fontSize = htmlRem + 'px'

      size.state.scaleNum = +scaleNum.toFixed(2)
      commit('WINDOW_RESIZE', screenHtml.clientHeight)
    }, 500)
  }
}

export default size
