<template>
  <div style="height: 100%">
    <a-input
      placeholder="请输入"
      @change="onChange"
      allowClear
      style="margin-left: 10px; margin-bottom: 10px; width: 200px"
    />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <!-- showIcon -->
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="true"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />

        <SvgIcon
          slot="catalogue"
          iconClass="tree-catalogue"
          class="depIcon"
          style="font-size: 18px; margin-bottom: -1px"
        />
        <SvgIcon slot="leaf" iconClass="tree-leaf" class="depIcon" style="font-size: 18px; margin-bottom: -1px" />

        <SvgIcon
          v-for="icon in allIcons"
          :key="icon"
          :slot="icon"
          :iconClass="icon"
          class="depIcon"
          style="font-size: 18px; margin-bottom: -1px"
        />

        <template slot="title" slot-scope="{ title }">
          <span v-if="title.indexOf(searchValue) > -1">
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style="color: #f50">{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else>{{ title }}</span>
        </template>
      </AntTree>
    </div>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'
  import { allIcons } from '@/assets/icons/index'

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  const districts = ['0', '1', '2', '3', '4', '5']
  function getIcon(ele) {
    // 行政区划icon
    if (districts.includes(ele.icon)) {
      return 'district_' + ele.icon
    }

    if (ele.children && ele.children.length > 0) {
      return 'catalogue'
    } else {
      if (ele.type === 'category') {
        if (ele?.icon) {
          return allIcons.includes(ele.icon) ? ele.icon : 'catalogue'
        }
        return 'catalogue'
      }
      if (ele.type === 'data') {
        if (ele?.icon) {
          return allIcons.includes(ele.icon) ? ele.icon : 'leaf'
        }
      }

      return 'leaf'
    }
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          slots: { icon: getIcon(ele) },
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'TreeGeneral',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
      currentKeys: { default: () => [] },
    },

    data() {
      return {
        loading: false,
        treeData: [],

        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,

        dataList: [],
        selectedKeys: this.currentKeys,

        allIcons: allIcons,
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    watch: {},
    methods: {
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true
        if (!this.treeOptions?.getDataApi) {
          this.loading = false

          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)

          // 设置默认选中第一个节点
          if (this.selectedKeys.length == 0) {
            this.selectedKeys = [this.treeData[0].key]
          }

          this.generateList(this.treeData)
          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit('onTreeMounted', this.treeData)
          }
        } else {
          setTimeout(() => {
            const searchInfo = { keywords: value }
            this.treeOptions
              .getDataApi(searchInfo)
              .then(response => {
                this.loading = false
                if (this.isLeafDisabled) {
                  this.treeData = handleTreeData(response?.data || [])
                }
                this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)

                console.log('treeData', this.treeData)
                // 设置默认选中第一个节点
                if (this.selectedKeys.length == 0) {
                  this.selectedKeys = [this.treeData[0].key]
                }

                this.generateList(this.treeData)

                this.getExpandedKeys(response.data)
                Object.assign(this, {
                  expandedKeys: this.leafNodes,
                  searchValue: value,
                  autoExpandParent: true,
                })
                this.leafNodes = []
                if (type === 'created') {
                  this.$emit('onTreeMounted', response?.data || [])
                }
              })
              .catch(res => {
                console.log('error', res)
              })
          }, 200)
        }
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        console.log(' 节点单击事件 handleNodeClick', keys, event)
        let obj = event.node.dataRef
        if (!keys?.length) return
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node, obj)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .loading {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    top: 30px;
  }
</style>
