<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="260"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model
          ref="form"
          :model="form"
          :rules="{ fileUrl: [{ required: true, message: '请选择文件', trigger: 'change' }] }"
        >
          <a-form-model-item
            label=""
            prop="fileUrl"
            ref="fileUrl"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <UploadFile
              :fileUrl="form.fileUrl"
              @update:fileUrl="onFileChange"
              :hasUuid="false"
              :multiple="false"
              listType="text"
              :folderName="`cloudFile/${form.parentId}`"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  import { addFile } from '../services'

  export default {
    name: 'UploadModal',
    props: {},
    components: { AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,

        // 弹框名称
        formTitle: '',
        open: false,

        // 表单参数
        form: {
          parentId: undefined,
          fileUrl: undefined,
          sizeByte: undefined,
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      onFileChange(filUrl, size) {
        this.form.fileUrl = filUrl
        this.form.sizeByte = size

        this.$nextTick(() => this.$refs.fileUrl.onFieldChange())
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
        this.form.parentId = parentId
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            addFile(this.form)
              .then(response => {
                this.$message.success('新增成功', 3)
                this.open = false
                this.$emit('close')
                this.$emit('ok')
              })
              .finally(() => (this.loading = false))
          } else {
            return false
          }
        })
      },
    },
  }
</script>
