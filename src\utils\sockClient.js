import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

export class SocketClient {
  socketIns = null
  stompClient = null
  subscription = null

  constructor() {}
  connect(topic, callback) {
    // 建立连接对象
    const url = process.env.VUE_APP_BASE_API + '/ws/notification'

    this.socketIns = new SockJS(url, { method: 'get' })
    // 使用STOMP来创建WebSocket客户端
    this.stompClient = Stomp.over(this.socketIns)
    //调用stompClient中的connect方法来连接服务端
    // 向服务器发起websocket连接并发送CONNECT帧

    let headers = {
      token: JSON.parse(localStorage.getItem('access_token')),
    }

    const that = this
    this.stompClient.connect(
      headers,
      function connectCallback(frame) {
        // 连接成功时（服务器响应 CONNECTED 帧）的回调方法
        console.log('连接成功')
        //调用stompClient中的subscribe方法来订阅/topic/getResponse发送来的消息
        //也就是我们在Controller中的broadcast方法上添加的@SendTo注解的参数。
        //stompClient中的send方法表示发送一条消息到服务端，
        // 客户端订阅消息的目的地址：此值BroadcastCtl中被@SendTo("/topic/subscribeTest")注解的里配置的值
        that.stompClient.subscribe(topic, response => {
          const res = JSON.parse(response.body)
          callback(res)
        })
      },
      function errorCallBack(error) {
        // 连接失败时（服务器响应 ERROR 帧）的回调方法
        console.log('连接失败')
      },
    )
  }

  // 断开连接
  disconnect() {
    if (this.stompClient != null) {
      this.stompClient.disconnect()
      this.unsubscribe()
      this.stompClient = null
    }
  }

  // 订阅连接断开事件
  clientOnDisconnectHandle() {
    this.closeStompClient() // 先关闭之前的socket连接
  }

  // 清除订阅
  unsubscribe() {
    if (this.subscription) {
      this.subscription.unsubscribe()
      this.subscription = null
    }
  }

  // 关闭之前的socket连接
  closeStompClient() {
    if (this.stompClient) {
      this.unsubscribe()
      this.stompClient = null
    }
  }
}
