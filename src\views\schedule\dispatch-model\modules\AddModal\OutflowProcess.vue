<template>
  <div style="height: 100%; display: flex; padding: 16px">
    <!-- 左侧控制面板和图表 -->
    <div style="width: 55%; padding-right: 12px">
      <!-- 出库参数来源方式（仅手动调度时显示） -->
      <div v-if="baseInfo.dispatchMethod === 3" style="margin-bottom: 16px">
        <h3 style="margin-bottom: 8px; font-weight: bold">出库参数来源方式：</h3>
        <div style="display: flex; align-items: center; gap: 12px;">
          <a-radio-group v-model="outflowSourceType" @change="changeOutflowSource" style="flex: 0.5">
            <a-radio :value="1">现状</a-radio>
            <a-radio :value="2">推荐</a-radio>
          </a-radio-group>
          
          <a-select
            v-if="outflowSourceType === 2"
            v-model="selectedRecommendPlan"
            placeholder="请选择推荐方案"
            style="flex: 1"
            @change="changeRecommendPlan"
          >
            <a-select-option v-for="plan in recommendPlans" :key="plan.value" :value="plan.value">
              {{ plan.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 调度方式说明 -->
      <!-- <div v-if="baseInfo.dispatchMethod" style="margin-bottom: 16px; padding: 8px; background: #f6f8fa; border-radius: 4px; font-size: 12px; color: #666;">
        <div v-if="baseInfo.dispatchMethod === 1" style="color: #666;">
          <strong>现状调度：</strong>数据不可编辑，显示历史调度数据
        </div>
        <div v-else-if="baseInfo.dispatchMethod === 2" style="color: #1890ff;">
          <strong>推荐调度：</strong>供水流量可手动调整，泄洪流量由系统推荐
        </div>
        <div v-else-if="baseInfo.dispatchMethod === 3" style="color: #52c41a;">
          <strong>手动调度：</strong>供水、泄洪流量均可手动调整，支持图表交互编辑
        </div>
      </div> -->

      <!-- 缩放系数控制 -->
      <div v-if="baseInfo.dispatchMethod !== 1" style="margin-bottom: 16px">
        <h3 style="margin-bottom: 8px;font-weight: bold; ">缩放系数：</h3>
        
        <div class="" style="display: flex;">
          <!-- 缩放对象选择（仅手动调度时显示） -->
          <div v-if="baseInfo.dispatchMethod === 3" style="margin-bottom: 8px">
            <a-select v-model="scaleTarget" style="flex:0.5">
              <a-select-option value="both">同步修改</a-select-option>
              <a-select-option value="supply">供水流量</a-select-option>
              <a-select-option value="flood">泄洪流量</a-select-option>
            </a-select>
          </div>
          
          <!-- 推荐调度提示 -->
          <!-- <div v-if="baseInfo.dispatchMethod === 2" style="margin-bottom: 8px; font-size: 12px; color: #666;">
            仅调整供水流量
          </div>
          -->
          <!-- 缩放系数控制器：滑动条和输入框在同一行 -->
          <div style="display: flex; align-items: center; gap: 12px; flex:1.5;">
            <a-slider
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              style="flex: 1;"
              @change="onScaleChange"
            />
            <a-input-number
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 80px"
              @change="onScaleChange"
            />
          </div>

          <!-- 总降雨量控制 -->
          <div style="display: flex; align-items: center; gap: 12px; margin-left: 16px;">
            <label style="white-space: nowrap;">总降雨量：</label>
            <a-input-number
              v-model="totalRainfall"
              :min="0"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 100px"
              @change="onTotalRainfallChange"
            />
            <span style="font-size: 12px; color: #666;">mm</span>
          </div>
        </div>
      </div>

      <!-- 折线图 -->
      <div style="height: 450px; border-radius: 4px;">
        <OutflowChart 
          :chartData="chartData" 
          :dispatchMethod="baseInfo.dispatchMethod"
          @dataChange="onChartDataChange"
          @toggleEdit="toggleEditMode"
        />
      </div>
    </div>

    <!-- 右侧数据表格 -->
    <div style="width: 45%; padding-left: 12px">
      <OutflowTable 
        :dataSource="tableData"
        :dispatchMethod="baseInfo.dispatchMethod"
        @dataChange="onTableDataChange"
      />
    </div>

  </div>
</template>

<script>
import OutflowChart from './OutflowChart.vue'
import OutflowTable from './OutflowTable.vue'
import moment from 'moment'

export default {
  name: 'OutflowProcess',
  components: {
    OutflowChart,
    OutflowTable
  },
  props: ['baseInfo'],
  data() {
    return {
      outflowSourceType: 1, // 1-现状, 2-推荐
      selectedRecommendPlan: undefined,
      recommendPlans: [
        { label: '推荐方案1', value: 1 },
        { label: '推荐方案2', value: 2 },
        { label: '推荐方案3', value: 3 },
      ],
      scaleTarget: 'both', // both-同步修改, supply-供水流量, flood-泄洪流量
      scaleValue: 1,
      originalData: [],
      tableData: [],
      editMode: false,
      importData: '',
      // 降雨相关数据
      rainfallData: [], // 降雨过程数据
      originalRainfallData: [], // 原始降雨数据
      totalRainfall: 0, // 总降雨量
      originalTotalRainfall: 0, // 原始总降雨量
    }
  },
  computed: {
    chartData() {
      return [
        {
          name: '供水流量',
          data: this.tableData.map(item => [item.time, item.supplyFlow]),
          color: '#1890ff'
        },
        {
          name: '泄洪流量', 
          data: this.tableData.map(item => [item.time, item.floodFlow]),
          color: '#52c41a'
        }
      ]
    }
  },
  created() {
    this.generateMockData()
    
    // 推荐调度时模拟获取实时数据
    if (this.baseInfo.dispatchMethod === 2) {
      this.loadRealtimeData()
    }
  },
  methods: {
    generateMockData() {
      // 生成测试数据
      const startTime = moment(this.baseInfo.startTime)
      const endTime = moment(this.baseInfo.endTime)
      const duration = endTime.diff(startTime, 'hours')

      this.tableData = []
      this.rainfallData = []
      let totalRain = 0

      for (let i = 0; i <= duration; i++) {
        const time = startTime.clone().add(i, 'hours').format('YYYY-MM-DD HH:mm')

        let supplyFlow, floodFlow

        if (this.baseInfo.dispatchMethod === 1) {
          // 现状调度：模拟历史数据
          supplyFlow = Math.round(70 + Math.random() * 30) // 70-100
          floodFlow = Math.round(30 + Math.random() * 20) // 30-50
        } else if (this.baseInfo.dispatchMethod === 2) {
          // 推荐调度：模拟实时数据（供水流量可调整）
          supplyFlow = Math.round(85 + Math.random() * 35) // 85-120
          floodFlow = Math.round(45 + Math.random() * 25) // 45-70（不可调整）
        } else {
          // 手动调度：初始数据
          supplyFlow = Math.round(80 + Math.random() * 40) // 80-120
          floodFlow = Math.round(40 + Math.random() * 30) // 40-70
        }

        // 生成降雨数据 - 模拟一个降雨过程
        let rainfall = 0
        if (i >= duration * 0.2 && i <= duration * 0.8) {
          // 在中间60%的时间段内有降雨
          const peakIndex = Math.floor(duration * 0.5) // 峰值在中间
          const distanceFromPeak = Math.abs(i - peakIndex)
          const maxDistance = Math.floor(duration * 0.3)
          if (distanceFromPeak <= maxDistance) {
            const intensity = 1 - (distanceFromPeak / maxDistance)
            rainfall = +(Math.random() * 15 * intensity).toFixed(1) // 0-15mm，越靠近峰值越大
          }
        }

        totalRain += rainfall

        this.tableData.push({
          time,
          supplyFlow,
          floodFlow,
        })

        this.rainfallData.push({
          time,
          rainfall,
        })
      }

      this.originalData = JSON.parse(JSON.stringify(this.tableData))
      this.originalRainfallData = JSON.parse(JSON.stringify(this.rainfallData))
      this.totalRainfall = +totalRain.toFixed(1)
      this.originalTotalRainfall = this.totalRainfall
    },

    // 模拟加载实时数据（推荐调度使用）
    loadRealtimeData() {
      // 模拟从实时数据接口获取供水流量数据
      setTimeout(() => {
        this.tableData = this.tableData.map(item => ({
          ...item,
          supplyFlow: Math.round(item.supplyFlow + (Math.random() - 0.5) * 10) // 在原值基础上小幅波动
        }))
        this.originalData = JSON.parse(JSON.stringify(this.tableData))

        // 同时更新降雨数据（模拟实时降雨数据的小幅变化）
        this.rainfallData = this.rainfallData.map(item => ({
          ...item,
          rainfall: Math.max(0, +(item.rainfall + (Math.random() - 0.5) * 2).toFixed(1)) // 小幅波动，确保非负
        }))
        this.originalRainfallData = JSON.parse(JSON.stringify(this.rainfallData))

        // 重新计算总降雨量
        const newTotal = this.rainfallData.reduce((sum, item) => sum + item.rainfall, 0)
        this.totalRainfall = +newTotal.toFixed(1)
        this.originalTotalRainfall = this.totalRainfall

        // this.$message.success('已获取最新实时供水流量数据')
      }, 1000)
    },

    changeOutflowSource() {
      if (this.outflowSourceType === 1) {
        // 现状数据
        this.generateMockData()
      }
      this.selectedRecommendPlan = undefined
    },

    changeRecommendPlan() {
      // 根据选择的推荐方案加载数据
      this.generateMockData()
      // 模拟推荐方案的数据稍有不同
      this.tableData = this.tableData.map(item => ({
        ...item,
        supplyFlow: Math.round(item.supplyFlow * 1.1),
        floodFlow: Math.round(item.floodFlow * 0.9),
      }))

      // 同时调整降雨数据
      this.rainfallData = this.rainfallData.map(item => ({
        ...item,
        rainfall: +(item.rainfall * 1.05).toFixed(1), // 推荐方案的降雨量稍微增加5%
      }))

      // 更新原始数据
      this.originalData = JSON.parse(JSON.stringify(this.tableData))
      this.originalRainfallData = JSON.parse(JSON.stringify(this.rainfallData))

      // 重新计算总降雨量
      const newTotal = this.rainfallData.reduce((sum, item) => sum + item.rainfall, 0)
      this.totalRainfall = +newTotal.toFixed(1)
      this.originalTotalRainfall = this.totalRainfall
    },

    onScaleChange() {
      if (this.baseInfo.dispatchMethod === 1) return // 现状调度不允许修改

      this.tableData = this.originalData.map(item => {
        const newItem = { ...item }

        // 推荐调度时只能调整供水流量
        if (this.baseInfo.dispatchMethod === 2) {
          newItem.supplyFlow = Math.round(item.supplyFlow * this.scaleValue)
          // 泄洪流量保持不变
          newItem.floodFlow = item.floodFlow
        } else {
          // 手动调度时根据选择的目标进行缩放
          if (this.scaleTarget === 'both' || this.scaleTarget === 'supply') {
            newItem.supplyFlow = Math.round(item.supplyFlow * this.scaleValue)
          }
          if (this.scaleTarget === 'both' || this.scaleTarget === 'flood') {
            newItem.floodFlow = Math.round(item.floodFlow * this.scaleValue)
          }
        }

        return newItem
      })

      // 同时更新降雨数据
      this.updateRainfallByScale()
    },

    // 处理总降雨量变化
    onTotalRainfallChange(newTotalRainfall) {
      if (this.baseInfo.dispatchMethod === 1) return // 现状调度不允许修改
      if (!newTotalRainfall || newTotalRainfall <= 0 || this.originalTotalRainfall <= 0) return

      // 计算倍数：新总降雨量 / 原始总降雨量
      const multiplier = newTotalRainfall / this.originalTotalRainfall

      // 更新缩放系数
      this.scaleValue = +multiplier.toFixed(1)

      // 更新降雨数据
      this.updateRainfallByScale()

      // 更新出库流量数据（如果需要联动）
      this.onScaleChange()
    },

    // 根据缩放系数更新降雨数据
    updateRainfallByScale() {
      this.rainfallData = this.originalRainfallData.map(item => ({
        ...item,
        rainfall: +(item.rainfall * this.scaleValue).toFixed(1)
      }))

      // 重新计算总降雨量
      this.totalRainfall = +(this.originalTotalRainfall * this.scaleValue).toFixed(1)
    },

    onTableDataChange(newData) {
      this.tableData = newData
      // 更新原始数据以便缩放功能正常工作
      this.originalData = JSON.parse(JSON.stringify(newData))
      this.scaleValue = 1

      // 重置降雨数据的缩放
      this.rainfallData = JSON.parse(JSON.stringify(this.originalRainfallData))
      this.totalRainfall = this.originalTotalRainfall
    },

    onChartDataChange(newData) {
      // 图表数据变化时更新表格数据
      this.tableData = this.tableData.map((item, index) => ({
        ...item,
        supplyFlow: Math.round(newData.supplyFlow[index] || item.supplyFlow),
        floodFlow: Math.round(newData.floodFlow[index] || item.floodFlow),
      }))

      // 同时更新原始数据以便缩放功能正常工作
      this.originalData = JSON.parse(JSON.stringify(this.tableData))
      this.scaleValue = 1

      // 重置降雨数据的缩放
      this.rainfallData = JSON.parse(JSON.stringify(this.originalRainfallData))
      this.totalRainfall = this.originalTotalRainfall
    },

    toggleEditMode() {
      this.editMode = !this.editMode
    },

    

    save() {
      this.$emit('saveData', {
        outflowSourceType: this.outflowSourceType,
        selectedRecommendPlan: this.selectedRecommendPlan,
        scaleValue: this.scaleValue,
        scaleTarget: this.scaleTarget,
        tableData: this.tableData,
        // 添加降雨相关数据
        rainfallData: this.rainfallData,
        totalRainfall: this.totalRainfall,
        originalTotalRainfall: this.originalTotalRainfall,
      })
    },
  }
}
</script>

<style lang="less" scoped>
</style> 