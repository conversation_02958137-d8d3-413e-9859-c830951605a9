<script lang="jsx">
  export default {
    name: 'VxeTableForm',
    props: [],
    components: {},
    data() {
      return {
        isShowAdvance: false,
        // 高级搜索 展开/关闭
        advanced: false,

        colNum: 3,
        mdNum: 8,
      }
    },
    creacte() {},
    directives: {
      // 使用局部注册指令的方式
      resize: {
        // 指令的名称
        bind(el, binding) {
          // el为绑定的元素，binding为绑定给指令的对象
          let width = '',
            height = ''
          function isReize() {
            const style = document.defaultView.getComputedStyle(el)
            if (width !== style.width || height !== style.height) {
              binding.value(style.width, style.height) // 关键
            }
            width = style.width
            height = style.height
          }
          el.__vueSetInterval__ = setInterval(isReize, 300)
        },
        unbind(el) {
          clearInterval(el.__vueSetInterval__)
        },
      },
    },
    mounted() {},
    methods: {
      toggleAdvanced() {
        this.advanced = !this.advanced

        if (this.advanced) {
          let rowNum = Math.ceil(this.$slots?.default?.length / this.colNum)

          if (this.$slots?.default?.length % this.colNum === 0) {
            this.$slots.table[0].context.$refs.vxeTableRef?.onChangeHeight(rowNum + 1)
          } else {
            this.$slots.table[0].context.$refs.vxeTableRef?.onChangeHeight(rowNum)
          }
        } else {
          this.$slots.table[0].context.$refs.vxeTableRef?.onChangeHeight(1)
        }
      },

      dealIsShowAdvance() {
        if (this.$slots?.default?.length >= 3 && this.colNum == 3) {
          this.isShowAdvance = true
        } else if (this.$slots?.default?.length >= 4 && this.colNum == 4) {
          this.isShowAdvance = true
        } else {
          this.isShowAdvance = false
        }
      },

      resize(width, height) {
        const wid = +width.substr(0, width.length - 2)
        if (wid >= 1400) {
          this.colNum = 4
          this.mdNum = 6
        } else if (wid < 1400) {
          this.colNum = 3
          this.mdNum = 8
        }

        this.dealIsShowAdvance()
      },
    },
    render() {
      const cols = this.$slots.default

      let btnMd = this.mdNum
      if (cols.length % this.colNum === 0) btnMd = btnMd * this.colNum
      if (cols.length % this.colNum === 1) btnMd = btnMd * (this.colNum - 1)
      if (cols.length % this.colNum === 2) btnMd = btnMd * (this.colNum - 2)
      if (cols.length % this.colNum === 3) btnMd = btnMd * (this.colNum - 3)

      if (!this.advanced) {
        if (cols.length < this.colNum) {
          btnMd = (this.colNum - cols.length) * this.mdNum
        } else {
          btnMd = this.mdNum
        }
      }

      return (
        <div class='vxe-table-form'>
          <a-card bordered={false} style='border-bottom:1px solid #F2F3F5;'>
            <div class='table-page-search-wrapper' v-resize={this.resize}>
              <a-form layout='inline'>
                <a-row gutter={40}>
                  {cols.slice(0, this.colNum - 1).map((v, idx) => {
                    return <a-col span={this.mdNum}>{v}</a-col>
                  })}

                  {this.advanced &&
                    cols.slice(this.colNum - 1, cols.length).map((v, idx) => {
                      return <a-col span={this.mdNum}>{v}</a-col>
                    })}

                  {/* 兼容一行和多行的按钮宽度 */}
                  <a-col span={btnMd}>
                    <span
                      class='table-page-search-submitButtons'
                      style={(this.advanced && { float: 'right', overflow: 'hidden' }) || {}}
                    >
                      <a-button
                        type='primary'
                        onClick={() => {
                          this.$listeners.handleQuery && this.$listeners.handleQuery()
                        }}
                      >
                        <a-icon type='search' />
                        查询
                      </a-button>
                      <a-button
                        style='margin-left: 8px'
                        onClick={() => {
                          this.$listeners.resetQuery && this.$listeners.resetQuery()
                        }}
                      >
                        <a-icon type='redo' />
                        重置
                      </a-button>
                      {this.isShowAdvance && (
                        <a onClick={this.toggleAdvanced} style='margin-left: 8px'>
                          {this.advanced ? '收起' : '展开'}
                          <a-icon type={this.advanced ? 'up' : 'down'} />
                        </a>
                      )}
                    </span>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-card>

          {this.$slots.table}
        </div>
      )
    },
  }
</script>

<style lang="less" scoped>
  .vxe-table-form {
    display: flex;
    height: 100%;
    flex-direction: column;
    overflow: visible;

    ::v-deep .vxe-table-content {
      flex: 1;
    }
  }
</style>
