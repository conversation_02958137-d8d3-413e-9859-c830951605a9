<template>
  <div style="height: 100%">
    <a-input-search placeholder="请输入" @search="onChange" />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <a-tree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :replaceFields="replaceFields"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        showIcon
        @select="handleNodeClick"
        @expand="onExpand"
      ></a-tree>
    </div>
  </div>
</template>
<script lang="jsx">
  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  export default {
    name: 'TreeGeneral',
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        loading: false,
        treeData: [],
        expandedKeys: this.defaultExpandedKeys,
        oldTreeData: [], // 记录查询前数据结构
        oldExpandedKeys: [],
        replaceFields: this.treeOptions.replaceFields,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    computed: {},
    watch: {},
    methods: {
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node[this.key])
          return this.getExpandedKeys(node.children)
        })
      },
      // 筛选节点
      onChange(value, e) {
        if (this.oldTreeData.length === 0) {
          this.oldTreeData = this.treeData
          this.oldExpandedKeys = this.expandedKeys
        }
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true
        if (this.treeOptions.dataSource?.length && type !== 'search') {
          this.treeData = this.treeOptions.dataSource

          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit('onTreeMounted', this.treeData)
          }
        } else {
          const searchInfo = { keywords: value }
          this.treeOptions
            .getDataApi(searchInfo)
            .then(response => {
              if (this.isLeafDisabled) {
                this.treeData = handleTreeData(response?.data || [])
              }
              this.treeData = response?.data || []

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })
              this.leafNodes = []
              if (type === 'created') {
                this.$emit('onTreeMounted', response?.data || [])
              }
            })
            .catch(res => {
              console.log('error', res)
            })
        }
        this.loading = false
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        this.$emit('select', event.node)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  .tree-panel-tree-box {
    position: relative;
    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
    }
  }
</style>
