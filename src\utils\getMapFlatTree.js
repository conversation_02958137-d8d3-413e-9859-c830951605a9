// 获取扁平树
export function getFlatTree(oldArr) {
  let temArr = []
  function getTreeTypes(arr) {
    arr?.forEach(el => {
      temArr.push(el)
      getTreeTypes(el.children)
    })
  }
  getTreeTypes(oldArr)
  return temArr
}

// 根据指定的key获取拍平的obj对象   obj[key] = item
export default function getFlatTreeMap(oldArr, key) {
  let temObj = {}
  function getTreeTypes(arr) {
    arr?.forEach(el => {
      temObj[el[key]] = el
      getTreeTypes(el.children)
    })
  }
  getTreeTypes(oldArr)
  return temObj
}

// 获取第一个leaf
export function getFirstLeaf(dataArr) {
  let obj
  function dealArr(arr) {
    arr.forEach(ele => {
      if (!obj) {
        if (ele.isLeaf || !ele.children?.length) {
          obj = ele
        } else {
          dealArr(ele.children || [])
        }
      }
    })
  }

  dealArr(dataArr)
  return obj
}
