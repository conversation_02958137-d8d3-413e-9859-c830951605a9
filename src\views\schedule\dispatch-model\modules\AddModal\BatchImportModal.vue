<template>
  <a-modal
    :title="dispatchMethod === 2 ? '批量导入供水流量' : '批量导入供水流量与泄洪流量'"
    :visible="visible"
    :width="dispatchMethod === 2 ? 700 : 900"
    :maskClosable="false"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="batch-import-container">
      <div class="tips-section">
        <a-alert
          message="操作提示"
          :description="tipsText"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </div>
      <div class="table-container">
        <div class="table-wrapper">
          <table class="excel-table" @paste="handlePaste" tabindex="0">
            <thead>
              <tr>
                <th class="time-column">时间</th>
                <th class="supply-column">供水流量(m³/s)</th>
                <th v-if="dispatchMethod === 3" class="flood-column">泄洪流量(m³/s)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableData" :key="index" :class="{ 'selected-row': isRowSelected(index) }">
                <td class="time-cell">{{ item.time }}</td>
                <td 
                  class="supply-cell"
                  :class="{ 'selected': isCellSelected(index, 'supplyFlow'), 'editing': editingCell.row === index && editingCell.field === 'supplyFlow' }"
                  @mousedown="handleMouseDown(index, 'supplyFlow', $event)"
                  @mouseover="handleMouseOver(index, 'supplyFlow')"
                  @mouseup="handleMouseUp"
                  @dblclick="startEdit(index, 'supplyFlow')"
                >
                  <div v-if="!(editingCell.row === index && editingCell.field === 'supplyFlow')" class="cell-display" @click="startEdit(index, 'supplyFlow')">
                    {{ item.supplyFlow || 0 }}
                  </div>
                  <input v-else ref="editInput" v-model.number="item.supplyFlow" type="number" step="1" min="0" class="cell-input" @focus="handleCellFocus(index, 'supplyFlow')" @blur="finishEdit" @keydown="handleInputKeyDown($event, index, 'supplyFlow')" />
                </td>
                <td v-if="dispatchMethod === 3"
                  class="flood-cell"
                  :class="{ 'selected': isCellSelected(index, 'floodFlow'), 'editing': editingCell.row === index && editingCell.field === 'floodFlow' }"
                  @mousedown="handleMouseDown(index, 'floodFlow', $event)"
                  @mouseover="handleMouseOver(index, 'floodFlow')"
                  @mouseup="handleMouseUp"
                  @dblclick="startEdit(index, 'floodFlow')"
                >
                  <div v-if="!(editingCell.row === index && editingCell.field === 'floodFlow')" class="cell-display" @click="startEdit(index, 'floodFlow')">
                    {{ item.floodFlow || 0 }}
                  </div>
                  <input v-else ref="editInput" v-model.number="item.floodFlow" type="number" step="1" min="0" class="cell-input" @focus="handleCellFocus(index, 'floodFlow')" @blur="finishEdit" @keydown="handleInputKeyDown($event, index, 'floodFlow')" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="summary-section">
        <div class="summary-item">
          <span>总时段数：</span>
          <span class="summary-value">{{ tableData.length }}</span>
        </div>
        <div class="summary-item">
          <span>已填充：</span>
          <span class="summary-value">{{ filledCount }}</span>
        </div>
      </div>
      <div class="footer-actions">
        <a-button @click="handleClear" style="margin-right: 8px;">清空数据</a-button>
        <a-button @click="handleCancel" style="margin-right: 8px;">取消</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'BatchImportModal',
  props: {
    visible: { type: Boolean, default: false },
    timeList: { type: Array, default: () => [] },
    dispatchMethod: { type: Number, default: 2 },
  },
  data() {
    return {
      tableData: [],
      selectedRange: { start: -1, end: -1, field: '' },
      isSelecting: false,
      focusedCell: { row: -1, field: '' },
      editingCell: { row: -1, field: '' },
    }
  },
  computed: {
    tipsText() {
      if (this.dispatchMethod === 2) {
        return '1. 单击或双击供水流量单元格可直接编辑数值；2. 支持粘贴Excel数据；3. 选择多行后可批量粘贴/清空/填充；4. 点击保存按钮将数据应用到主表格';
      } else {
        return '1. 单击或双击供水流量/泄洪流量单元格可直接编辑数值；2. 支持粘贴Excel数据（两列）；3. 选择多行后可批量粘贴/清空/填充；4. 点击保存按钮将数据应用到主表格';
      }
    },
    filledCount() {
      if (this.dispatchMethod === 2) {
        return this.tableData.filter(item => item.supplyFlow > 0).length
      } else {
        return this.tableData.filter(item => item.supplyFlow > 0 || item.floodFlow > 0).length
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initTableData()
        this.$nextTick(() => {
          const table = this.$el.querySelector('.excel-table')
          if (table) table.focus()
        })
      }
    },
    timeList: {
      handler() {
        if (this.visible) this.initTableData()
      },
      deep: true
    }
  },
  methods: {
    initTableData() {
      this.tableData = this.timeList.map(time => ({
        time,
        supplyFlow: 0,
        floodFlow: this.dispatchMethod === 3 ? 0 : undefined
      }))
      this.selectedRange = { start: -1, end: -1, field: '' }
    },
    handleMouseDown(index, field, event) {
      event.preventDefault()
      this.isSelecting = true
      this.selectedRange = { start: index, end: index, field }
      document.addEventListener('selectstart', this.preventSelection)
    },
    handleMouseOver(index, field) {
      if (this.isSelecting) {
        this.selectedRange.end = index
        this.selectedRange.field = field
      }
    },
    handleMouseUp() {
      this.isSelecting = false
      document.removeEventListener('selectstart', this.preventSelection)
    },
    preventSelection(e) {
      e.preventDefault()
      return false
    },
    handleCellFocus(index, field) {
      this.focusedCell = { row: index, field }
      if (this.selectedRange.start === -1) {
        this.selectedRange = { start: index, end: index, field }
      }
    },
    startEdit(index, field) {
      if (this.isSelecting) return
      this.editingCell = { row: index, field }
      this.focusedCell = { row: index, field }
      this.$nextTick(() => {
        const input = this.$refs.editInput
        if (input && input.length) {
          const targetInput = Array.isArray(input) ? input[0] : input
          targetInput.focus()
          targetInput.select()
        }
      })
    },
    finishEdit() {
      this.editingCell = { row: -1, field: '' }
      this.focusedCell = { row: -1, field: '' }
    },
    handleInputKeyDown(event, index, field) {
      switch (event.key) {
        case 'Enter':
          event.preventDefault()
          this.finishEdit()
          if (index < this.tableData.length - 1) this.startEdit(index + 1, field)
          break
        case 'Escape':
          event.preventDefault()
          this.finishEdit()
          break
        case 'Tab':
          event.preventDefault()
          this.finishEdit()
          if (!event.shiftKey && index < this.tableData.length - 1) this.startEdit(index + 1, field)
          else if (event.shiftKey && index > 0) this.startEdit(index - 1, field)
          break
        case 'ArrowDown':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index < this.tableData.length - 1) this.startEdit(index + 1, field)
          }
          break
        case 'ArrowUp':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index > 0) this.startEdit(index - 1, field)
          }
          break
      }
    },
    isRowSelected(index) {
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      return index >= start && index <= end && start !== -1
    },
    isCellSelected(index, field) {
      return this.isRowSelected(index) && this.selectedRange.field === field
    },
    handlePaste(event) {
      event.preventDefault()
      if (this.selectedRange.start === -1) {
        this.$message.warning('请先选择要粘贴的区域')
        return
      }
      const clipboardData = event.clipboardData || window.clipboardData
      const pastedText = clipboardData.getData('text')
      if (!pastedText.trim()) {
        this.$message.warning('粘贴内容为空')
        return
      }
      this.processPastedData(pastedText)
    },
    processPastedData(text) {
      const lines = text.trim().split(/\r?\n/)
      const values = []
      lines.forEach(line => {
        const cells = line.split(/\t|,|\s+/).filter(cell => cell.trim())
        if (this.dispatchMethod === 2) {
          // 推荐调度：只粘贴供水流量
          if (cells.length > 0) values.push({ supplyFlow: parseFloat(cells[0]) })
        } else {
          // 手动调度：粘贴供水流量和泄洪流量
          if (cells.length > 1) values.push({ supplyFlow: parseFloat(cells[0]), floodFlow: parseFloat(cells[1]) })
        }
      })
      if (values.length === 0) {
        this.$message.warning('未识别到有效的数值数据')
        return
      }
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      let valueIndex = 0
      for (let i = start; i <= end && i < this.tableData.length; i++) {
        if (valueIndex < values.length) {
          this.tableData[i].supplyFlow = values[valueIndex].supplyFlow || 0
          if (this.dispatchMethod === 3) {
            this.tableData[i].floodFlow = values[valueIndex].floodFlow || 0
          }
          valueIndex++
        } else {
          // 粘贴数据不够，循环使用
          this.tableData[i].supplyFlow = values[valueIndex % values.length].supplyFlow || 0
          if (this.dispatchMethod === 3) {
            this.tableData[i].floodFlow = values[valueIndex % values.length].floodFlow || 0
          }
          valueIndex++
        }
      }
      this.$message.success(`已成功粘贴 ${Math.min(values.length, end - start + 1)} 行数据`)
    },
    handleClear() {
      this.$confirm({
        title: '确认清空',
        content: '确定要清空所有数据吗？',
        onOk: () => {
          this.tableData.forEach(item => {
            item.supplyFlow = 0
            if (this.dispatchMethod === 3) item.floodFlow = 0
          })
          this.selectedRange = { start: -1, end: -1, field: '' }
          this.$message.success('已清空所有数据')
        }
      })
    },
    handleSave() {
      const hasData = this.tableData.some(item => item.supplyFlow > 0 || (this.dispatchMethod === 3 && item.floodFlow > 0))
      if (!hasData) {
        this.$message.warning('请先填入数据')
        return
      }
      const result = this.tableData.map(item => ({
        time: item.time,
        supplyFlow: item.supplyFlow || 0,
        floodFlow: this.dispatchMethod === 3 ? (item.floodFlow || 0) : undefined
      }))
      this.$emit('save', result)
      this.handleCancel()
    },
    handleCancel() {
      this.$emit('update:visible', false)
      this.selectedRange = { start: -1, end: -1, field: '' }
      this.isSelecting = false
      this.focusedCell = { row: -1, field: '' }
      this.editingCell = { row: -1, field: '' }
    }
  }
}
</script>

<style lang="less" scoped>
.batch-import-container {
  .tips-section {
    margin-bottom: 16px;
  }
  .table-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;
    .table-wrapper {
      position: relative;
    }
    .excel-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      outline: none;
      th, td {
        border: 1px solid #e8e8e8;
        padding: 8px 12px;
        text-align: left;
        position: relative;
      }
      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
        position: sticky;
        top: 0;
        z-index: 10;
      }
      .time-column {
        width: 40%;
        min-width: 150px;
      }
      .supply-column {
        width: 30%;
        min-width: 120px;
      }
      .flood-column {
        width: 30%;
        min-width: 120px;
      }
      .time-cell {
        background-color: #f5f5f5;
        color: #666;
        font-family: 'Consolas', 'Monaco', monospace;
      }
      .supply-cell, .flood-cell {
        cursor: pointer;
        user-select: none;
        transition: all 0.2s ease;
        position: relative;
        &:hover {
          background-color: rgba(22, 93, 255, 0.1);
        }
        &.selected {
          background-color: rgba(22, 93, 255, 0.2);
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }
        &.editing {
          background-color: #fff;
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }
        .cell-display {
          width: 100%;
          height: 100%;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          min-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: text;
          &:hover {
            background-color: rgba(22, 93, 255, 0.05);
          }
        }
        .cell-input {
          width: 100%;
          border: none;
          outline: none;
          background: transparent;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;
        }
      }
      .selected-row {
        .time-cell {
          background-color: rgba(22, 93, 255, 0.1);
        }
      }
      .supply-cell.selected, .flood-cell.selected {
        box-shadow: inset 0 0 0 2px #165DFF;
      }
    }
  }
  .summary-section {
    display: flex;
    gap: 24px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
    .summary-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      span:first-child {
        color: #666;
        margin-right: 4px;
      }
      .summary-value {
        font-weight: 600;
        color: #262626;
      }
    }
  }
  .footer-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
  }
}
// 滚动条样式
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  &:hover {
    background: #a8a8a8;
  }
}
</style> 