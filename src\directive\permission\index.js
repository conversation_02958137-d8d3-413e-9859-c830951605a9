import store from '@/store'

const hasPermi = {
  inserted(el, bindling) {
    //bindling.value为指令的绑定值
    let perVal = bindling.value

    if (bindling.value) {
      //hasPer为true为有权限
      //hasPer为false为无权限
      let hasPer = store.getters.permissionButtons.some(item => {
        return item == perVal
      })
      //没有权限就先隐藏此元素吧
      if (!hasPer) {
        el.style.display = 'none'
      }
    }
  },
}

export const setPermissionDirective = function (Vue) {
  Vue.directive('permission', hasPermi)
}
