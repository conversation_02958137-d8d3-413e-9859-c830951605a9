<template>
  <div class="container">
    <a-row>
      <a-col :span="12">
        <a-form-model-item has-feedback :label="tip" :prop="prop">
          <a-input-password v-model="pwdee" id="inputValue" :placeholder="'请输入' + tip" />
        </a-form-model-item>
      </a-col>
      <a-col :span="10">
        <PasswordCheck :newpwd="pwdee" :style="{ width: '240px', 'margin-top': '40px', 'margin-left': '15px' }" />
      </a-col>
    </a-row>
  </div>
</template>

<script lang="jsx">
  import { PasswordCheck } from '@/components'
  export default {
    props: {
      tip: { type: String, default: '密码' },
      prop: String
    },
    components: { PasswordCheck },
    data() {
      return { msgText: '', pwdee: '' }
    },
    methods: {},
    watch: {}
  }
</script>

<style scoped>
  #inputValue {
    width: 240px;
    margin-left: 20px;
    padding-left: 10px;
    border-radius: 3px;
  }

  #font span:nth-child(1) {
    color: red;
    margin-left: 80px;
  }
  #font span:nth-child(2) {
    color: orange;
    margin: 0 60px;
  }
  #font span:nth-child(3) {
    color: #00d1b2;
  }
</style>
