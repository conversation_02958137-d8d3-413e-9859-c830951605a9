import './ThemeColor.less'

import PropTypes from 'ant-design-vue/es/_util/vue-types'
import { genThemeToString } from '../../utils/util'
import 'ant-design-vue/es/tooltip/style'
import Tooltip from 'ant-design-vue/es/tooltip'
import 'ant-design-vue/es/icon/style'
import Icon from 'ant-design-vue/es/icon'

const baseClassName = 'theme-color'

export const TagProps = {
  color: PropTypes.string,
  check: PropTypes.bool
}

const Tag = {
  props: TagProps,
  functional: true,
  render(h, content) {
    const {
      props: { color, check },
      data
    } = content
    return (
      <div {...data} style={{ backgroundColor: color }}>
        {check ? <Icon type='check' /> : null}
      </div>
    )
  }
}

export const ThemeColorProps = {
  colors: PropTypes.array,
  title: PropTypes.string,
  value: PropTypes.string
}

const ThemeColor = {
  props: ThemeColorProps,
  render(h) {
    const { title, value, colors = [] } = this
    const handleChange = key => {
      this.$emit('change', key)
    }

    const colorTypes = {
      daybreak: '拂晓蓝',
      dust: '薄暮',
      volcano: '火山',
      sunset: '日暮',
      cyan: '明青',
      green: '极光绿',
      geekblue: '极客蓝',
      purple: '酱紫'
    }

    return (
      <div class={baseClassName} ref={'ref'}>
        <h3 class={`${baseClassName}-title`}>{title}</h3>
        <div class={`${baseClassName}-content`}>
          {colors.map(item => {
            const themeKey = genThemeToString(item.key)
            const check = value === item.key || genThemeToString(value) === item.key
            return (
              <Tooltip key={item.color} title={themeKey ? colorTypes[themeKey] : item.key}>
                <Tag
                  class={`${baseClassName}-block`}
                  color={item.color}
                  check={check}
                  onClick={() => handleChange(item.key)}
                />
              </Tooltip>
            )
          })}
        </div>
      </div>
    )
  }
}

export default ThemeColor
