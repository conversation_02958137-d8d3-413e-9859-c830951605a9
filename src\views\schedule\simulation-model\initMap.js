import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import { getValueByKey } from '@/api/common.js'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import * as turf from '@turf/turf'

export default function initMap(mapIns, parentIns) {
  // 天地图
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-base-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[0].id,
  )
  // 天地图标注
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-label-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[1].id,
  )

  // 灌区边界
  getValueByKey('gis.irr.boundary').then(res => {
    axios(res.data).then(resp => {
      mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
      mapIns.addLayer({
        id: 'gq-area-line',
        type: 'line',
        source: {
          type: 'geojson',
          data: resp.data, //区划的面数据
        },
        paint: {
          'line-color': '#03FFCD',
          'line-width': 2,
        },
      })
    })
  })

  let paiShui = "https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3Asthgq_paishuiqu&maxFeatures=5000&outputFormat=application%2Fjson"
  let hoveredPolygonId = null;
  axios(paiShui).then(resp => {
    // mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
    mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
    mapIns.addSource('paiShuiSource', {
      type: 'geojson',
      data: resp.data, //区划的面数据
      'generateId': true // 确保所有特征都有唯一的ID
    })

    mapIns.addLayer({
      id: 'gq-area-paiShui',
      type: 'fill',
      source: "paiShuiSource",
      paint: {
        'fill-color': '#00804f', // blue color fill
        'fill-opacity': [
          'case',
          ['boolean', ['feature-state', 'hover'], false],
          1,
          0.4
        ]
      }
    })
    mapIns.addLayer({
      'id': 'gq-area-paiShui-borders',
      'type': 'line',
      'source': 'paiShuiSource',
      'layout': {},
      'paint': {
        'line-color': '#00804f',
        'line-width': 2
      }
    });

    mapIns.addInteraction('gq-area-paiShui-click-interaction', {
      type: 'click',
      target: { layerId: 'gq-area-paiShui' },
      handler: (e) => {
        // Copy coordinates array.
        const coordinates =turf.centroid(turf.polygon(e.feature.geometry.coordinates)).geometry.coordinates;
        const description = e.feature.properties.name;
        if(parentIns){
          console.log(description+"集水区1111111111")
          parentIns.getAreaDataByName(description+"集水区")  
        }
        new mapboxgl.Popup()
          .setLngLat(coordinates)
          .setHTML(description)
          .addTo(mapIns);
      }
    });

    mapIns.on('mousemove', 'gq-area-paiShui', (e) => {
      if (e.features.length > 0) {
        if (hoveredPolygonId !== null) {
          mapIns.setFeatureState(
            { source: 'paiShuiSource', id: hoveredPolygonId },
            { hover: false }
          );
        }
        hoveredPolygonId = e.features[0].id;
        mapIns.setFeatureState(
          { source: 'paiShuiSource', id: hoveredPolygonId },
          { hover: true }
        );
      }
    });

  })
  mapIns.on('mouseleave', 'gq-area-paiShui', () => {
    if (hoveredPolygonId !== null) {
      mapIns.setFeatureState(
        { source: 'paiShuiSource', id: hoveredPolygonId },
        { hover: false }
      );
    }
    hoveredPolygonId = null;
  });

}
