<template>
  <div style="height: 100%">
    <a-input placeholder="请输入" @change="onChange" allowClear />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="false"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <template slot="title" slot-scope="{ title }">
          <span v-if="title.indexOf(searchValue) > -1" class="tree-node-title" :title="title">
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style="color: #f50">{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else class="tree-node-title" :title="title">{{ title }}</span>
        </template>
      </AntTree>
    </div>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'OrgGeneral',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        loading: false,
        treeData: [],
        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,

        dataList: [],
        selectedKeys: [],
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    watch: {},
    methods: {
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        if (this.selectedKeys.length == 0) {
          this.selectedKeys = [JSON.parse(localStorage.getItem('user'))?.deptId]
        }

        this.loading = true
        if (this.treeOptions.dataSource?.length && type !== 'search') {
          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)

          this.generateList(this.treeData)

          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit('onTreeMountedOrg', this.treeData)
          }
        } else {
          const searchInfo = { keywords: value }
          this.treeOptions
            .getDataApi(searchInfo)
            .then(response => {
              if (this.isLeafDisabled) {
                this.treeData = handleTreeData(response?.data || [])
              }
              this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)
              this.generateList(this.treeData)

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })
              this.leafNodes = []
              if (type === 'created') {
                this.$emit('onTreeMountedOrg', response?.data || [])
              }
            })
            .catch(res => {
              console.log('error', res)
            })
        }
        this.loading = false
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #f2f3f5; //#aaa;
  }
  ::v-deep .ant-tree {
    width: 130px !important;
    min-width: 130px !important;
    max-width: 160px !important;

    // background: red;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  ::v-deep .tree-panel-tree-box {
    position: relative;

    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
    }
  }

  .tree-node-title {
    width: 127px;
    // min-width: 130px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    // min-width: 120px !important;
    // max-width: 160px !important;

    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 1;
    // -webkit-box-orient: vertical;
    // cursor: pointer;
  }
</style>
