import * as XLSX from 'xlsx-js-style'

export const dealBorderAndCenter = (arr, columns) => {
  return arr.map(item =>
    item.map((el, index) => ({
      v: el === null ? '' : el,
      t: 's',
      s: {
        alignment: {
          horizontal: columns?.[index]?.align || 'left',
          vertical: 'center',
        },
        border: {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } },
        },
      },
    })),
  )
}

export const getHeaderAndBody = (columns, data) => {
  const bodyMapList = []

  function getBodyMapList(list) {
    if (list?.length) {
      list.forEach(item => {
        if (!item.children) {
          bodyMapList.push(item.field)
        } else {
          getBodyMapList(item.children)
        }
      })
    }
  }
  getBodyMapList(columns)

  const headerValue = [columns.map(item => item.title)]
  const bodyValue = data.map(item => bodyMapList.map(key => item[key]))

  return { headerValue, bodyValue }
}

function excelExport(columns, data, xlsxTitle) {
  const { headerValue, bodyValue } = getHeaderAndBody(columns, data)

  const header = dealBorderAndCenter(headerValue, columns)
  const body = dealBorderAndCenter(bodyValue, columns)

  const ws = XLSX.utils.json_to_sheet([])

  ws['!cols'] = Object.keys(bodyValue[0]).map((el, i) => ({ wpx: columns[i].minWidth }))

  XLSX.utils.sheet_add_aoa(ws, header, { origin: 'A1' })
  XLSX.utils.sheet_add_aoa(ws, body, { origin: 'A2' })

  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

  XLSX.writeFile(wb, `${xlsxTitle}.xlsx`)
}

export default excelExport
