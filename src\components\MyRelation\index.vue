<template>
  <div class="relation-content">
    <div class="top">{{ projectName }}</div>
    <div class="mid-line" v-if="dataSource.length > 0"></div>

    <div class="item-box">
      <div class="item" v-for="(el, i) in dataSource" :key="i" @click="() => onItemClick(el)">
        <div class="border-up-empty">
          <span></span>
        </div>
        <div class="name">{{ el.value }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'MyRelation',
    props: {
      projectName: {},
      dataSource: {
        default: () => []
      }
    },
    data() {
      return {}
    },
    watch: {},
    created() {},
    mounted() {},
    methods: {
      onItemClick(item) {
        console.log(item)
        this.$emit('click', item)
      }
    }
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .relation-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .top {
      padding: 5px 20px;
      color: #ffffff;
      background-color: @primary-color;
      position: relative;
      border-radius: 2px;
      border-bottom: 5px solid #0251d2;
      margin-bottom: 40px;
      &::before {
        position: absolute;
        content: '';
        width: 2px;
        height: 40px;
        left: 50%;
        bottom: -45px;
        transform: translate(-50%);
        background-color: @primary-color;
      }
    }
    .mid-line {
      width: calc(100% - 36px); //减去一个项的宽度
      height: 2px;
      background-color: @primary-color;
      position: relative;
      margin-bottom: 40px;

      &::before {
        position: absolute;
        content: '';
        width: 2px;
        height: 40px;
        left: 0;
        bottom: -40px;
        background-color: @primary-color;
      }
      &::after {
        position: absolute;
        content: '';
        width: 2px;
        height: 40px;
        right: 0;
        bottom: -40px;
        background-color: @primary-color;
      }
    }

    .item-box {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item {
        position: relative;
        // overflow: hidden;
        margin-top: -1px;
        cursor: pointer;
        .name {
          text-align: center;
          writing-mode: vertical-lr; //从左往右
          letter-spacing: 2px;
          border: 2px solid @primary-color;
          border-top: none;
          padding: 5px;
          max-height: 180px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: @primary-color;
          background-color: #e7effe;
          width: 36px;
          box-shadow: 3px 4px 5px @primary-color;
        }

        .border-up-empty {
          margin-left: -1px;
          width: 0;
          height: 0;
          border-left: 19px solid transparent;
          border-right: 19px solid transparent;
          border-bottom: 17px solid @primary-color;
          position: relative;
        }
        .border-up-empty span {
          display: block;
          width: 0;
          height: 0;
          border-left: 16px solid transparent;
          border-right: 16px solid transparent;
          border-bottom: 14px solid #e7effe;
          position: absolute;
          left: -16px;
          top: 3px;
        }

        &::before {
          position: absolute;
          content: '';
          width: 31px;
          height: 2px;
          left: 50%;
          transform: translate(-50%);
          top: 14px;
          background-color: @primary-color;
          z-index: 3;
        }
      }
    }
  }
</style>
