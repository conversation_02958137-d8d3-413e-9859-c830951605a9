@import './default.less';

body {
  font-family: 'Microsoft YaHei', 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
  a {
    color: @text-color;
  }
  .ant-table,
  .ant-tree,
  .ant-select,
  .ant-input,
  .ant-select-selection--multiple .ant-select-selection__choice {
    color: @text-color;
    font-size: @font-size-base;
  }
  .rehearsal-table {
    .ant-table-default .ant-table-thead > tr > th,
    .ant-table-default .ant-table-tbody > tr > td {
      padding: 8px 3px !important;
    }
  }
  .ant-table-default .ant-table-thead > tr > th:not(:first-child) {
    padding-left: 13px !important;
  }
  .ant-select-disabled,
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
  }
  .ant-input-affix-wrapper i.anticon {
    opacity: 0;
    transition: all 0.3s;
  }
  .ant-calendar-picker:hover,
  .ant-input-affix-wrapper:hover {
    i.anticon {
      transition: all 0.3s;
      opacity: 1;
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-tree {
    li {
      padding: 2px 0;

      span.ant-tree-switcher {
        width: 16px;
        height: 16px;
        line-height: 24px;
      }
      span.ant-tree-iconEle {
        width: 20px;
        height: 20px;
        line-height: 24px;
      }
    }
    .ant-tree-child-tree > li:first-child {
      padding: 2px 0;
    }
    .ant-tree-child-tree.ant-tree-child-tree-open {
      padding-left: 12px;
    }
  }

  .ant-tree.ant-tree-show-line li:not(:last-child)::before {
    left: 8px;
  }

  .ant-tree li .ant-tree-node-content-wrapper {
    color: @text-color;
    padding-left: 0px;
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper {
    color: @text-color;
  }

  .ant-card-body {
    padding: 0;
  }
  .ant-modal-title {
    color: @heading-color;
  }
  .table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 16px;
  }
  .table-page-search-wrapper {
    .ant-form-inline .ant-form-item > .ant-form-item-label {
      line-height: @input-height-base;
    }
  }
  .ant-table-default .ant-table-thead > tr > th,
  .ant-table-default .ant-table-tbody > tr > td {
    padding: 16px 8px;
  }
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td,
  .ant-table-placeholder {
  }
  .ant-table-wrapper {
    margin: 0 12px;
  }
  .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
  .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
  .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
  .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-header
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-header
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
  .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
  .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td,
  .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-header
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-header
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-small
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-tbody
    > tr
    > td {
    padding: 4px 4px;
  }
  .ant-table-middle > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
  .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
  .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
  .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-header
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-header
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-thead
    > tr
    > th,
  .ant-table-middle > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
  .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
  .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td,
  .ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-header
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-header
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-left
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-tbody
    > tr
    > td,
  .ant-table-middle
    > .ant-table-content
    > .ant-table-fixed-right
    > .ant-table-body-outer
    > .ant-table-body-inner
    > table
    > .ant-table-tbody
    > tr
    > td {
    padding: 8px 8px;
    line-height: 24px;
  }
  // .ant-table-thead > tr > th {
  //   background: #f7f7f7;
  // }
  .ant-table-small > .ant-table-content > .ant-table-body {
    margin: 0;
  }
  .table-operations {
    margin: 0;
  }
  .ant-pro-global-header {
    height: @layout-header-height;
  }
  .ant-pro-global-header-trigger {
    height: @layout-header-height;
    line-height: @layout-header-height;
  }
  .ant-layout-header {
    height: @layout-header-height;
    padding: 0 @layout-header-height;
    line-height: @layout-header-height;
  }
  .ant-pro-global-header-content {
    height: @layout-header-height;
    line-height: @layout-header-height;
  }
  .ant-pro-drop-down {
    line-height: @layout-header-height;
  }
  .ant-form-explain {
    font-size: @font-size-base;
  }
  .ant-page-header-heading-title {
    font-size: @spin-dot-size-sm;
    line-height: 28px;
  }
  .ant-form-extra {
    font-size: @font-size-base;
    min-height: 18px;
  }
  .ant-page-header.has-breadcrumb {
    padding: 14px 0;
  }
  .ant-pro-global-header-index-right .ant-pro-global-header-index-action {
    padding: 0;
  }
  .ant-pro-top-nav-header-menu .ant-menu.ant-menu-horizontal,
  .ant-pro-top-nav-header .ant-menu-submenu.ant-menu-submenu-horizontal,
  .ant-pro-top-nav-header-logo,
  .ant-pro-sider-menu-logo {
    height: @layout-header-height;
    line-height: @layout-header-height;
  }
  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    background: #f0f2f5;
    border-bottom: 1px solid #f0f2f5;
  }
  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    height: 36px;
    margin-right: 4px;
    margin-top: 4px;
    line-height: 36px;
  }

  .ant-menu-inline-collapsed {
    width: 100%;
  }
  .ant-menu-inline-collapsed > .ant-menu-item,
  .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
  .ant-menu-inline-collapsed
    > .ant-menu-item-group
    > .ant-menu-item-group-list
    > .ant-menu-submenu
    > .ant-menu-submenu-title,
  .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
    padding: 0 20px !important;
  }

  .ant-pro-sider-menu-logo img {
    width: 38px;
    height: 38px;
  }
  .ant-layout .ant-table-pagination.ant-pagination {
    margin: 12px 0 4px 0;
  }
  .ant-pagination {
    .ant-select {
      min-height: auto;
    }
  }

  .table-page-search-wrapper {
    padding: 16px;
    padding-bottom: 0;
  }
  .table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control,
  .ant-select-selection--single,
  .ant-input {
    height: @input-height-base;
    line-height: @input-height-base;
    border-color: @border-color-base;
  }

  .advanced-table .header-bar .title[data-v-6ac60990] {
    font-size: @font-size-base + 2;
    font-weight: 400;
    color: @heading-color;
  }
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    min-width: @input-height-base;
    height: @input-height-base;
  }

  .ant-form-item-label > label {
    color: @text-color;
  }
  .ant-menu-vertical .ant-menu-item,
  .ant-menu-vertical-left .ant-menu-item,
  .ant-menu-vertical-right .ant-menu-item,
  .ant-menu-inline .ant-menu-item,
  .ant-menu-vertical .ant-menu-submenu-title,
  .ant-menu-vertical-left .ant-menu-submenu-title,
  .ant-menu-vertical-right .ant-menu-submenu-title,
  .ant-menu-inline .ant-menu-submenu-title {
    font-size: 14px;
    color: @heading-color;
    .anticon {
      font-size: 16px;
      vertical-align: middle;
    }
  }

  .ant-menu-submenu-selected > .ant-menu-submenu-title {
    color: @primary-color;
  }
  .ant-menu-inline-collapsed > .ant-menu-item .anticon,
  .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item .anticon,
  .ant-menu-inline-collapsed
    > .ant-menu-item-group
    > .ant-menu-item-group-list
    > .ant-menu-submenu
    > .ant-menu-submenu-title
    .anticon,
  .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .anticon {
    font-size: 18px;
  }
  .ant-menu-item > a {
    color: @heading-color;
  }
  .ant-menu-item-selected > a,
  .ant-menu-item-selected > a:hover {
    color: @primary-color;
  }
  .ant-table,
  .ant-form {
    font-size: @font-size-base;
  }
  input.ant-form {
    font-size: @font-size-base;
  }
  .ant-form-item {
    font-size: @font-size-base;
  }
  .has-error .ant-form-explain,
  .has-error .ant-form-split {
    font-size: @font-size-sm;
  }
  .ant-form label,
  .ant-input,
  .ant-select {
    font-size: @font-size-base;
  }
  .ant-form-item-label {
    line-height: 1.5;
  }
  .ant-select-selection {
    border-top-width: 1px;
  }
  .ant-select-selection--multiple {
    min-height: calc(@input-height-base - 4px);
    padding-bottom: 2px;
  }
  .ant-select-selection--multiple > ul > li,
  .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
    margin-top: 2px;
    height: calc(@input-height-base - 6px);
    line-height: calc(@input-height-base - 8px);
  }
  .ant-radio-button-wrapper {
    height: @input-height-base;
  }
  .ant-select-selection--multiple .ant-select-selection__rendered {
    margin-left: 2px;
  }
  .ant-select-selection__rendered {
    line-height: calc(@input-height-base - 2px);
  }
  .ant-form-sm label,
  .ant-input-sm,
  .ant-select-sm,
  .ant-form-item-label-sm {
    font-size: @font-size-base;
    height: @input-height-sm;
    line-height: @input-height-sm;
  }
  .ant-form-lg label,
  .ant-input-lg,
  .ant-select-lg,
  .ant-form-item-label-lg {
    font-size: @font-size-base;
    height: @input-height-lg;
    line-height: @input-height-lg;
  }

  .ant-form .ant-form-item .ant-form-item-control {
    line-height: inherit;
  }
  .ant-modal-content .ant-modal-body:has(.ant-modal-confirm-body-wrapper) {
    background: linear-gradient(155deg, rgba(255, 125, 0, 0.2) 5%, rgba(255, 125, 0, 0) 40%);
  }
  .ant-modal-close-x {
    height: 42px;
    line-height: 42px;
    font-size: 14px;
  }
  .ant-modal-header {
    background: rgba(
      170,
      196,
      255,
      0.25
    ); //linear-gradient(90deg, rgba(88, 139, 255, 0.24) 1%, rgba(88, 139, 255, 0) 100%);
    border-bottom: 1px dashed #c4c2c2;
    padding: 10px 10px;

    // .ant-modal-title {
    //   font-weight: 700;
    // }
    .aidex-modal-size-adjust {
      height: 42px;
      line-height: 42px;
      margin-right: -24px;
    }
  }
  .ant-modal-footer {
    padding: 16px;
    border-top: none;
  }
  .ant-pro-global-header {
    box-shadow: none;
    transform: none;
    transition: none;
    background: left bottom no-repeat url('~@/assets/images/header-bg2.png');
  }
  .ant-pro-sider-menu-sider.light .ant-pro-sider-menu-logo {
    padding-left: 10px;
    box-shadow: none;
    transform: none;
    transition: none;
    background: right bottom no-repeat url('~@/assets/images/header-bg1.png');
  }
  .ant-pro-sider-menu-sider.light .ant-pro-sider-menu-logo h1 {
    color: #fff;
  }
  .ant-pro-page-header-wrap-page-header-warp {
    background-color: inherit;
    color: #fff;
  }
  .ant-space-align-center {
    color: #fff;
  }
  .ant-pro-global-header .ant-pro-global-header-index-action i,
  .ant-pro-global-header-trigger {
    color: #fff;
  }
  .ant-breadcrumb {
    color: rgba(255, 255, 255, 0.65);
  }
  .ant-breadcrumb a {
    color: rgba(255, 255, 255, 0.65);
  }
  .ant-breadcrumb-separator {
    color: rgba(255, 255, 255, 0.65);
  }
  .ant-breadcrumb > span:last-child {
    color: rgba(255, 255, 255, 0.95);
  }
  .ant-list-item-meta-description a {
    color: @text-color;
  }
  .ant-list-item-meta-description a:hover {
    color: @primary-color;
  }
  .ant-btn.ant-btn-link {
    color: @primary-color;
    border: 0;
    padding: 0;
  }
  .ant-btn {
    color: @text-color;
    border-color: @border-color-base;
  }
  .ant-btn-sm {
    font-size: 12px;
  }
  .ant-btn-primary.ant-btn {
    border-color: @primary-color;
    color: #fff;
  }
  .ant-btn-primary.ant-btn[disabled] {
    border-color: @border-color-base;
    color: @text-color;
  }
  .ant-btn-danger.ant-btn {
    border-color: @error-color;
    color: #fff;
  }
  .ant-btn-background-ghost.ant-btn-danger {
    color: @error-color;
  }
  .ant-btn-danger.ant-btn[disabled] {
    border-color: @border-color-base;
    color: @text-color;
  }
  .ant-btn-background-ghost.ant-btn-primary {
    color: @primary-color;
  }
  .ant-table-row i {
    padding-right: 8px;
    &.depIcon {
      font-size: 20px;
      color: @primary-color;
      line-height: 20px;
      height: 20px;
      vertical-align: middle;
      margin-top: -4px;
    }
  }
  .ant-table-row-cell-break-word {
    a {
      color: @primary-color;
    }
  }

  .ant-collapse-borderless > .ant-collapse-item:last-child {
    &.ant-collapse-item-active {
      .ant-collapse-header {
        margin-bottom: 16px;
      }
    }
  }
  .ant-avatar {
    font-size: @font-size-sm;
  }
  .ant-avatar-sm {
    width: 32px;
    height: 32px;
    margin: calc((50px - 32px) / 2) !important;
  }
  /* 树形控件样式 */
  // .ant-tree {
  //   li {
  //     padding: 2px 0;
  //     span.ant-tree-switcher,
  //     .ant-tree li span.ant-tree-iconEle {
  //       vertical-align: middle;
  //       width: 18px;
  //       line-height: 32px;
  //     }
  //     .ant-tree-node-content-wrapper {
  //       padding: 0 4px;
  //       height: 32px;
  //       line-height: 32px;
  //       width: calc(100% - 20px);
  //       .ant-tree-iconEle {
  //         vertical-align: middle;
  //       }
  //       .depIcon {
  //         color: #2b2f36;
  //       }
  //     }
  //     .ant-tree-node-selected {
  //       .depIcon,
  //       .ant-tree-title {
  //         color: @primary-color;
  //       }
  //     }
  //     .ant-tree-node-content-wrapper.ant-tree-node-selected {
  //       background-color: @primary-1;
  //     }
  //     .ant-tree-title {
  //       padding: 0 4px;
  //     }
  //   }
  //   .ant-tree-child-tree > li:first-child {
  //     padding: 0;
  //   }
  // }

  /* 导航栏修改 */
  .ant-menu-inline .ant-menu-item {
    margin: 0;
  }
  .ant-menu-vertical .ant-menu-item:not(:last-child),
  .ant-menu-vertical-left .ant-menu-item:not(:last-child),
  .ant-menu-vertical-right .ant-menu-item:not(:last-child),
  .ant-menu-inline .ant-menu-item:not(:last-child) {
    margin-bottom: 0;
  }
  /* 弹出form表单修改 */
  .ant-form-vertical {
    .ant-form-item {
      padding-bottom: 0;
    }
    .ant-form-item-required::before {
      // position: absolute;
      // right: 0;
      // top: 4px;
    }
    .ant-form-item-required {
      padding-right: 14px;
    }
  }
  // 折叠面板样式修改
  .ant-collapse-borderless > .ant-collapse-item:last-child .ant-collapse-header {
    padding: 0 16px;
    color: @primary-color;
  }
  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    // left: 0;
  }
  // 折叠表格样式修改
  tr.ant-table-expanded-row,
  tr.ant-table-expanded-row:hover {
    background: #fafafa;
    .ant-card {
      padding: 8px;
      background: inherit;
    }
    .ant-table-wrapper {
      margin: 0;
    }
    .table-operations {
      padding-bottom: 8px;
      text-align: right;
    }
    .table_title {
      float: left;
      padding-left: 8px;
      font-weight: @typography-title-font-weight;
      color: @heading-color;
      line-height: @btn-height-base;
    }
  }
  .vditor-toolbar {
    padding: 0 8px !important;
  }
  .vditor-reset {
    padding: 8px !important;
  }
  .selectIconBox {
    border: 1px solid @border-color-base;
    border-radius: @border-radius-base;
    height: @input-height-base;
    width: 100%;
    .anticon {
      color: @primary-color;
      font-size: 16px;
      padding: 0 12px;
    }
    .selectup {
      .anticon {
        color: @text-color;
        font-size: @font-size-base;
      }
    }
    .ant-space-item {
      margin: 0 !important;
    }
    .selectup {
      position: absolute;
      right: 0;
      top: -2px;
      width: 80%;
      text-align: right;
    }
  }
}
.vxe-table--render-default {
  color: rgba(0, 0, 0, 0.88) !important;
}
.manage-unit {
  .ant-tabs {
    .ant-tabs-bar {
      border-bottom: none;
    }
    .ant-tabs-nav {
      margin: 0 20px;
      .ant-tabs-ink-bar {
        display: none !important;
      }
    }
    .ant-tabs-tab {
      height: 32px;
      margin-right: 10px;
      line-height: 15px;
      background: #f2f3f5;
    }
    .ant-tabs-nav .ant-tabs-tab-active {
      color: #fff;
      background: #165dff;
    }
    .ant-tabs-nav .ant-tabs-tab-active:active {
      color: #fff;
    }
  }
}
// .manage-unit-tabs {
//   .ant-tabs-bar {
//     border-bottom: none;
//   }
//   .ant-tabs-nav {
//     margin: 0 20px;
//     .ant-tabs-ink-bar {
//       display: none !important;
//     }
//   }
//   .ant-tabs-tab {
//     height: 32px;
//     margin-right: 10px;
//     line-height: 15px;
//     background: #f2f3f5;
//   }
//   .ant-tabs-nav .ant-tabs-tab-active {
//     color: #fff;
//     background: #165dff;
//   }
//   .ant-tabs-nav .ant-tabs-tab-active:active {
//     color: #fff;
//   }
// }
