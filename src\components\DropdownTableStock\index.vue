<template>
  <div>
    <a-table
      :columns="columns"
      rowKey="goodsId"
      size="middle"
      :data-source="data"
      ref="table"
      :pagination="false"
      bordered
      :customRow="customRow"
      class="table"
      :scroll="{ y: 560 }"
      :rowClassName="setRowNewStyle"
    ></a-table>
  </div>
</template>

<script lang="jsx">
  import { getStockPage } from '../../views/warehouse/out_warehouse-manager/services'
  import * as _ from 'lodash'

  export default {
    name: 'DropdownTableStock',
    props: ['changeData', 'goodsName', 'warehouseId', 'selectList'],
    data() {
      return {
        total: 0,
        selectedOption: '', // 当前选中的选项
        columns: [
          // 表格列定义
          {
            title: '物料名称',
            dataIndex: 'goodsName',
            align: 'left',
            width: '110px',
            key: 'goodsName',
          },
          {
            title: '物料编码',
            dataIndex: 'goodsCode',
            align: 'left',
            width: '110px',
            key: 'goodsCode',
          },
          {
            title: '物料型号',
            dataIndex: 'goodsType',
            align: 'left',
            width: '110px',
            key: 'goodsType',
          },
          {
            title: '物料规格',
            dataIndex: 'goodsSpec',
            align: 'left',
            width: '110px',
            key: 'goodsSpec',
          },
          {
            title: '物料库存',
            dataIndex: 'stockQty',
            align: 'left',
            width: '110px',
            key: 'stockQty',
          },
          {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            align: 'left',
            width: '100px',
            key: 'unitPrice',
          },
        ],
        goodsParam: {
          deptId: null,
          goodsName: this.goodsName,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          warehouseId: this.warehouseId,
        },
        data: [], // 表格数据源，其中每一行的 type 属性用于判断是否为可选项
      }
    },
    created() {},
    beforeDestroy() {
      this.goodsName = ''
      this.warehouseId = null
    },
    watch: {
      goodsName: {
        handler(newVal, oldVal) {
          this.data = []
          this.goodsName = newVal
          this.getList()
        },
        immediate: true,
      },
      warehouseId: {
        handler(newVal, oldVal) {
          this.data = []
          this.warehouseId = newVal
          this.getList()
        },
        immediate: true,
      },
    },
    methods: {
      setRowNewStyle(record) {
        if (record.stockQty == 0) {
          return 'newRowStyle'
        } else if (record.stockQty > 0) {
          return
        }
      },
      getList() {
        // if (!this.goodsName) {
        //   return
        // }
        this.goodsParam = {
          deptId: null,
          goodsName: this.goodsName,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          warehouseId: this.warehouseId,
        }
        getStockPage(this.goodsParam).then(res => {
          let orgList = res.data.data
          this.data = orgList.filter(obj => {
            return this.selectList.every(selObj => selObj.goodsId != obj.goodsId)
          })
          this.total = this.data.length
        })
      },
      // 分页
      handleChange(current, pageSize) {
        this.goodsParam.pageNum = current
        this.goodsParam.pageSize = pageSize
        this.getList()
      },
      handleCheck(value) {
        if (value.stockQty == 0) {
          // this.$message.warn(`请选择库存数量不为0的物料！`, 3)
          return
        }

        this.childData = value
        this.$emit('changeData', value)
      },
      customRow(record) {
        return {
          props: {
            // xxx... //属性
          },
          on: {
            // 事件
            click: event => {
              this.handleCheck(record)
            }, // 点击行
            dblclick: event => {},
            contextmenu: event => {},
            mouseenter: event => {}, // 鼠标移入行
            mouseleave: event => {},
          },
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  .dropdown-table {
    padding-top: 10px;
  }
  .table {
    padding-top: 10px;
  }
  /*.table 为全局表格自定义样式*/
  .table .ant-table-body,
  .table .ant-table-header {
    overflow-y: auto !important;
  }

  ::v-deep .newRowStyle {
    color: #bfbfbf !important;
    // background-color: #aaaaaa !important;
  }

  ::v-deep .newRowStyle:hover > td {
    cursor: not-allowed !important;
    color: #bfbfbf !important;
    // background-color: #aaaaaa !important;
  }

  ::v-deep .ant-table-tbody > tr > td {
    cursor: pointer !important;
  }
</style>
