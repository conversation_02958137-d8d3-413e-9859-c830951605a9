/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.9 (2023-11-15)
 */
!function(){"use strict";function C(e){var t=e;return{get:function(){return t},set:function(e){t=e}}}function u(e){return e.getParam("spellchecker_rpc_url")}function E(e){var t=new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+',"g");return e.getParam("spellchecker_wordchar_pattern",t)}function p(e){return e&&1===e.nodeType&&"false"===e.contentEditable}function r(i,r){function o(e){var t=i.getElementsByTagName("*"),n=[];e="number"==typeof e?""+e:null;for(var r=0;r<t.length;r++){var o=t[r],a=o.getAttribute("data-mce-index");null!==a&&a.length&&-1!==o.className.indexOf("mce-spellchecker-word")&&(a!==e&&null!==e||n.push(o))}return n}function a(e){for(var t=c.length;t--;)if(c[t]===e)return t;return-1}var n,c=[],v=r.dom,f=r.schema.getBlockElements(),h=r.schema.getWhiteSpaceElements(),g=r.schema.getShortEndedElements(),s=function(e){if(3===e.nodeType)return e.data;if(h[e.nodeName]&&!f[e.nodeName])return"";if(p(e))return"\n";var t="";if((f[e.nodeName]||g[e.nodeName])&&(t+="\n"),e=e.firstChild)for(;t+=s(e),e=e.nextSibling;);return t};function e(e){for(var t=0,n=c.length;t<n&&!1!==e(c[t],t);t++);return this}function t(e){for(var t=o(e?a(e):null),n=t.length;n--;)!function(e){for(var t=e.parentNode;0<e.childNodes.length;)t.insertBefore(e.childNodes[0],e);t.removeChild(e)}(t[n]);return this}function l(e){var t=o(a(e)),n=r.dom.createRng();return n.setStartBefore(t[0]),n.setEndAfter(t[t.length-1]),n}var u=s(i);return{text:u,matches:c,each:e,filter:function(n){var r=[];return e(function(e,t){n(e,t)&&r.push(e)}),c=r,this},reset:function(){return c.splice(0,c.length),t(),this},matchFromElement:function(e){return c[e.getAttribute("data-mce-index")]},elementFromMatch:function(e){return o(a(e))[0]},find:function(e,t){if(u&&e.global)for(;n=e.exec(u);)c.push(function(e,t){if(!e[0])throw new Error("findAndReplaceDOMText cannot handle zero-length matches");return{start:e.index,end:e.index+e[0].length,text:e[0],data:t}}(n,t));return this},add:function(e,t,n){return c.push({start:e,end:e+t,text:u.substr(e,t),data:n}),this},wrap:function(e){return c.length&&function(e,t,n){var r,o,a,i,c,s=[],l=0,u=e,d=0;(t=t.slice(0)).sort(function(e,t){return e.start-t.start}),c=t.shift();e:for(;;){if((f[u.nodeName]||g[u.nodeName]||p(u))&&l++,3===u.nodeType&&(!o&&u.length+l>=c.end?(o=u,i=c.end-l):r&&s.push(u),!r&&u.length+l>c.start&&(r=u,a=c.start-l),l+=u.length),r&&o){if(u=n({startNode:r,startNodeIndex:a,endNode:o,endNodeIndex:i,innerNodes:s,match:c.text,matchIndex:d}),l-=o.length-i,o=r=null,s=[],d++,!(c=t.shift()))break}else if(h[u.nodeName]&&!f[u.nodeName]||!u.firstChild){if(u.nextSibling){u=u.nextSibling;continue}}else if(!p(u)){u=u.firstChild;continue}for(;;){if(u.nextSibling){u=u.nextSibling;break}if(u.parentNode===e)break e;u=u.parentNode}}}(i,c,(o=e,function(e){var t=e.startNode,n=e.endNode,r=e.matchIndex,o=v.doc;if(t===n){var a=t,i=a.parentNode;0<e.startNodeIndex&&(s=o.createTextNode(a.data.substring(0,e.startNodeIndex)),i.insertBefore(s,a));var c=m(e.match,r);return i.insertBefore(c,a),e.endNodeIndex<a.length&&(l=o.createTextNode(a.data.substring(e.endNodeIndex)),i.insertBefore(l,a)),a.parentNode.removeChild(a),c}for(var s=o.createTextNode(t.data.substring(0,e.startNodeIndex)),l=o.createTextNode(n.data.substring(e.endNodeIndex)),u=m(t.data.substring(e.startNodeIndex),r),d=0,f=e.innerNodes.length;d<f;++d){var h=e.innerNodes[d],g=m(h.data,r);h.parentNode.replaceChild(g,h)}var p=m(n.data.substring(0,e.endNodeIndex),r);return(i=t.parentNode).insertBefore(s,t),i.insertBefore(u,t),i.removeChild(t),(i=n.parentNode).insertBefore(p,n),i.insertBefore(l,n),i.removeChild(n),p})),this;function m(e,t){var n=c[t];n.stencil||(n.stencil=o(n));var r=n.stencil.cloneNode(!1);return r.setAttribute("data-mce-index",""+t),e&&r.appendChild(v.doc.createTextNode(e)),r}var o},unwrap:t,replace:function(e,t){var n=l(e);return n.deleteContents(),0<t.length&&n.insertNode(r.dom.doc.createTextNode(t)),n},rangeFromMatch:l,indexOf:a}}function l(e,t){var n;return t.get()||(n=r(e.getBody(),e),t.set(n)),t.get()}function k(e,t,n,r,o,a,i){var c,s,l;(e.getParam("spellchecker_callback")||(c=e,s=t,l=n,function(e,t,r,o){var n={method:e,lang:l.get()},a="";n["addToDictionary"===e?"word":"text"]=t,M.each(n,function(e,t){a&&(a+="&"),a+=t+"="+encodeURIComponent(e)}),h.send({url:new f(s).toAbsolute(u(c)),type:"post",content_type:"application/x-www-form-urlencoded",data:a,success:function(e){var t,n=JSON.parse(e);n?n.error?o(n.error):r(n):(t=c.translate("Server response wasn't proper JSON."),o(t))},error:function(){var e=c.translate("The spelling service was not found: (")+u(c)+c.translate(")");o(e)}})})).call(e.plugins.spellchecker,r,o,a,i)}function R(t,e,n,r,o,a){i(t,n,r)||(t.setProgressState(!0),k(t,e,a,"spellcheck",l(t,r).text,function(e){O(t,n,r,o,e)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1),i(t,n,r)}),t.focus())}function y(e,t,n){e.dom.select("span.mce-spellchecker-word").length||i(e,t,n)}function S(t,e,n,r,o,a){t.selection.collapse(),a?M.each(t.dom.select("span.mce-spellchecker-word"),function(e){e.getAttribute("data-mce-word")===r&&t.dom.remove(e,!0)}):t.dom.remove(o,!0),y(t,e,n)}function b(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t}function _(g,p,m,v,x,N){g.ui.registry.addContextMenu("spellchecker",{update:function(e){var t=e;if("mce-spellchecker-word"!==t.className)return[];var c,s,n,l,u,d,f,h,r,o,a=function(e,t){var n=[],r=M.toArray(e.getBody().getElementsByTagName("span"));if(r.length)for(var o=0;o<r.length;o++){var a=b(r[o]);null!==a&&a.length&&a===t.toString()&&n.push(r[o])}return n}(g,b(t));if(0<a.length){var i=g.dom.createRng();return i.setStartBefore(a[0]),i.setEndAfter(a[a.length-1]),g.selection.setRng(i),c=g,s=p,n=m,l=v,u=x,d=N,f=t.getAttribute("data-mce-word"),h=a,r=[],o=n.get().suggestions[f],M.each(o,function(e){r.push({text:e,onAction:function(){c.insertContent(c.dom.encode(e)),c.dom.remove(h),y(c,l,u)}})}),n.get().hasDictionarySupport&&(r.push({type:"separator"}),r.push({text:"Add to dictionary",onAction:function(){var t,e=s,n=l,r=u,o=d,a=f,i=h;(t=c).setProgressState(!0),k(t,e,o,"addToDictionary",a,function(){t.setProgressState(!1),t.dom.remove(i,!0),y(t,n,r)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1)})}})),r.push.apply(r,[{type:"separator"},{text:"Ignore",onAction:function(){S(c,l,u,f,h)}},{text:"Ignore all",onAction:function(){S(c,l,u,f,h,!0)}}]),r}}})}var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=Object.hasOwnProperty,M=tinymce.util.Tools.resolve("tinymce.util.Tools"),f=tinymce.util.Tools.resolve("tinymce.util.URI"),h=tinymce.util.Tools.resolve("tinymce.util.XHR"),i=function(e,t,n){var r=e.selection.getBookmark();if(l(e,n).reset(),e.selection.moveToBookmark(r),n.set(null),t.get())return t.set(!1),e.fire("SpellcheckEnd"),!0},O=function(t,e,n,r,o){var a=!!o.dictionary,i=o.words;if(t.setProgressState(!1),function(e){for(var t in e)if(d.call(e,t))return;return 1}(i)){var c=t.translate("No misspellings found.");return t.notificationManager.open({text:c,type:"info"}),void e.set(!1)}r.set({suggestions:i,hasDictionarySupport:a});var s=t.selection.getBookmark();l(t,n).find(E(t)).filter(function(e){return!!i[e.text]}).wrap(function(e){return t.dom.create("span",{class:"mce-spellchecker-word","aria-invalid":"spelling","data-mce-bogus":1,"data-mce-word":e.text})}),t.selection.moveToBookmark(s),e.set(!0),t.fire("SpellcheckStart")},D=function(){return(D=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},F="SpellcheckStart SpellcheckEnd";e.add("spellchecker",function(e,t){if(0==(!!e.hasPlugin("tinymcespellchecker",!0)&&(void 0!==window.console&&window.console.log&&window.console.log("Spell Checker Pro is incompatible with Spell Checker plugin! Remove 'spellchecker' from the 'plugins' option."),!0))){var n=C(!1),r=C((I=(P=e).getParam("language","en"),P.getParam("spellchecker_language",I))),o=C(null),a=C(null);return c=e,s=t,l=n,u=o,d=r,f=a,h=M.map(c.getParam("spellchecker_languages","English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv").split(","),function(e){var t=e.split("=");return{name:t[0],value:t[1]}}),g=[],M.each(h,function(e){g.push({selectable:!0,text:e.name,data:e.value})}),p=g,v=D(D({},m={tooltip:"Spellcheck",onAction:i,icon:"spell-check",onSetup:function(e){function t(){e.setActive(l.get())}return c.on(F,t),function(){c.off(F,t)}}}),{type:"splitbutton",select:function(e){return e===d.get()},fetch:function(e){e(M.map(p,function(e){return{type:"choiceitem",value:e.data,text:e.text}}))},onItemAction:function(e,t){d.set(t)}}),1<p.length?c.ui.registry.addSplitButton("spellchecker",v):c.ui.registry.addToggleButton("spellchecker",m),c.ui.registry.addToggleMenuItem("spellchecker",{text:"Spellcheck",icon:"spell-check",onSetup:function(e){function t(){e.setActive(l.get())}return e.setActive(l.get()),c.on(F,t),function(){c.off(F,t)}},onAction:i}),_(e,t,a,n,o,r),b=t,w=n,T=o,A=a,B=r,(S=e).addCommand("mceSpellCheck",function(){R(S,b,w,T,A,B)}),x=e,N=n,k=a,{getTextMatcher:(y=o).get,getWordCharPattern:function(){return E(x)},markErrors:function(e){O(x,N,y,k,e)},getLanguage:r.get}}function i(){R(c,s,l,u,f,d)}var c,s,l,u,d,f,h,g,p,m,v,x,N,k,y,S,b,w,T,A,B,P,I})}();