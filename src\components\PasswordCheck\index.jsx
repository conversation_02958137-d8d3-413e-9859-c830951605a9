import './index.less'

export default {
  name: 'PasswordCheck',
  props: {
    newpwd: {
      type: String
    },
    styles: {}
  },
  data() {
    return {}
  },

  watch: {
    newpwd(newname, oldname) {
      if (!document.getElementById('weak')?.style) return
      this.msgText = this.checkStrong(newname)
      if (this.msgText === 'level1') {
        document.getElementById('weak').style.background = 'red'
      } else {
        document.getElementById('weak').style.background = '#cccccc'
      }
      if (this.msgText === 'level2') {
        document.getElementById('mezzo').style.background = 'orange'
      } else {
        document.getElementById('mezzo').style.background = '#cccccc'
      }
      if (this.msgText === 'level3') {
        document.getElementById('strong').style.background = '#00D1B2'
      } else {
        document.getElementById('strong').style.background = '#cccccc'
      }
    }
  },

  methods: {
    checkStrong(sValue) {
      var modes = 0
      // 正则表达式验证符合要求的
      if (sValue.length < 6) return 'level1'
      if (sValue.length > 1 && sValue.length < 6) modes++
      if (sValue.length > 8) {
        modes = modes + 2
      }
      if (/\d/.test(sValue)) modes++ // 数字
      if (/[a-z]/.test(sValue)) modes++ // 小写
      if (/[A-Z]/.test(sValue)) modes++ // 大写
      if (/\W/.test(sValue)) modes++ // 特殊字符

      // 小于6位--1--弱
      // 6~8位--(1~5)--中或强
      // 大于8位--(2~6)--中或强

      // 1~2--弱  3~4--中  5~6--强

      if (modes >= 1 && modes <= 2) {
        return 'level1'
      }
      if (modes >= 3 && modes <= 4) {
        return 'level2'
      }
      if (modes >= 5 && modes <= 6) {
        return 'level3'
      }
    }
  },
  render() {
    const { styles } = this.$props
    return (
      <div class='input_span' style={styles}>
        <label>安全强度:</label>
        <span id='weak'>弱</span>
        <span id='mezzo'>中</span>
        <span id='strong'>强</span>
      </div>
    )
  }
}
