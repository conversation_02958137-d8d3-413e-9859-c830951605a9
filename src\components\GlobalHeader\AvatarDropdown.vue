<template>
  <a-dropdown v-if="name" placement="bottomRight">
    <span class="ant-pro-account-avatar">
      <a-avatar size="small" v-show="avatar" :src="avatar" class="antd-pro-global-header-index-avatar" />
      <a-avatar
        size="small"
        v-show="!avatar"
        class="antd-pro-global-header-index-avatar"
        style="color: #fff; background-color: #ff8801; padding: 4% 0"
      >
        {{ name.length > 2 ? name.substring(name.length - 2) : name }}
      </a-avatar>
      <span>{{ name }}</span>
    </span>
    <template v-slot:overlay>
      <a-menu class="ant-pro-drop-down menu" :selected-keys="[]">
        <a-menu-item key="logout" @click="handleLogout">
          <a-icon type="logout" />
          退出登录
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <span v-else>
    <a-spin size="small" :style="{ marginLeft: 8, marginRight: 8 }" />
  </span>
</template>

<script lang="jsx">
  import { Modal } from 'ant-design-vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'AvatarDropdown',
    props: {
      menu: {
        type: Boolean,
        default: true,
      },
    },
    computed: {
      ...mapGetters(['avatar', 'name']),
    },
    methods: {
      handleLogout(e) {
        Modal.confirm({
          title: '提示',
          content: '确定注销并退出系统吗？',
          onOk: () => {
            return this.$store.dispatch('Logout').then(() => {})
          },
          onCancel() {},
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .ant-pro-drop-down {
    ::v-deep .action {
      margin-right: 8px;
    }

    ::v-deep .ant-dropdown-menu-item {
      min-width: 160px;
    }
  }
</style>
