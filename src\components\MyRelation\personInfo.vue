<template>
  <div class="person-content" @click="onClick">
    <div class="top">
      <div class="icon">
        <a-icon type="user" />
      </div>
    </div>
    <div class="content">
      <div class="info">
        <a-tooltip>
          <template slot="title">
            {{ dataSource.positionName }}
          </template>
          <div class="post">{{ dataSource.positionName || '　' }}</div>
        </a-tooltip>
        <div>姓名: {{ dataSource.personnelName }}</div>
        <div>手机: {{ dataSource.telephone }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'PersonInfo',
    props: {
      dataSource: { default: () => {} }
    },
    methods: {
      onClick() {
        this.$emit('click', this.dataSource)
      }
    }
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .person-content {
    width: 170px;
    position: relative;
    box-shadow: 2px 2px 5px @primary-color;
    margin-top: 24px;
    cursor: pointer;

    .top {
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      top: -24px;
      width: 50px;
      height: 25px;
      background-color: @primary-color;
      border-radius: 25px 25px 0 0;
      .icon {
        position: absolute;
        width: 48px;
        height: 24px;
        left: 50%;
        transform: translate(-50%);
        background-color: #e7effe;
        top: 1px;
        border-radius: 24px 24px 0 0;
        color: @primary-color;
        font-size: 20px;
        font-weight: 700;
        text-align: center;
      }
    }
    .content {
      padding: 8px;
      border: 1px solid @primary-color;
      background-color: #e7effe;
      border-radius: 5px 5px 0 5px;

      .post {
        color: @primary-color;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .info {
        background-color: #fff;
        border-radius: 3px;
        padding: 3px 8px;
        line-height: 26px;
      }
    }
  }
</style>
